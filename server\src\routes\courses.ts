import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { courseController } from '@/controllers/courseController'
import { authMiddleware, authorize, optionalAuth, checkCourseInstructor, checkCourseEnrollment } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validateRequest'
import { uploadCourseContent } from '@/middleware/upload'

const router = Router()

// Validation rules
const createCourseValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('category')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters'),
  body('level')
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED'])
    .withMessage('Level must be BEGINNER, INTERMEDIATE, or ADVANCED'),
  body('duration')
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (minutes)'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a non-negative number'),
]

const updateCourseValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('category')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters'),
  body('level')
    .optional()
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED'])
    .withMessage('Level must be BEGINNER, INTERMEDIATE, or ADVANCED'),
  body('duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (minutes)'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a non-negative number'),
]

const courseIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid course ID format'),
]

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('category')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Category cannot be empty'),
  query('level')
    .optional()
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED'])
    .withMessage('Level must be BEGINNER, INTERMEDIATE, or ADVANCED'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Search query cannot be empty'),
]

// Public routes
router.get('/', optionalAuth, queryValidation, validateRequest, courseController.getCourses)
router.get('/categories', courseController.getCategories)
router.get('/featured', courseController.getFeaturedCourses)
router.get('/:id', optionalAuth, courseIdValidation, validateRequest, courseController.getCourseById)
router.get('/:id/preview', courseIdValidation, validateRequest, courseController.getCoursePreview)

// Protected routes - require authentication
router.use(authMiddleware)

// Student routes
router.post('/:id/enroll', courseIdValidation, validateRequest, courseController.enrollInCourse)
router.delete('/:id/enroll', courseIdValidation, validateRequest, courseController.unenrollFromCourse)
router.get('/:id/enrollment', courseIdValidation, validateRequest, courseController.getEnrollmentStatus)
router.post('/:id/review', courseIdValidation, validateRequest, courseController.addCourseReview)

// Instructor routes
router.post('/', authorize('INSTRUCTOR', 'ADMIN'), createCourseValidation, validateRequest, courseController.createCourse)
router.put('/:id', courseIdValidation, updateCourseValidation, validateRequest, checkCourseInstructor, courseController.updateCourse)
router.delete('/:id', courseIdValidation, validateRequest, checkCourseInstructor, courseController.deleteCourse)
router.post('/:id/publish', courseIdValidation, validateRequest, checkCourseInstructor, courseController.publishCourse)
router.post('/:id/unpublish', courseIdValidation, validateRequest, checkCourseInstructor, courseController.unpublishCourse)
router.post('/:id/thumbnail', courseIdValidation, validateRequest, checkCourseInstructor, uploadCourseContent.single('thumbnail'), courseController.uploadCourseThumbnail)

// Course analytics (instructor and admin only)
router.get('/:id/analytics', courseIdValidation, validateRequest, checkCourseInstructor, courseController.getCourseAnalytics)
router.get('/:id/students', courseIdValidation, validateRequest, checkCourseInstructor, courseController.getCourseStudents)
router.get('/:id/progress', courseIdValidation, validateRequest, checkCourseInstructor, courseController.getCourseProgress)

// My courses (enrolled or created)
router.get('/my/enrolled', courseController.getMyEnrolledCourses)
router.get('/my/created', authorize('INSTRUCTOR', 'ADMIN'), courseController.getMyCreatedCourses)
router.get('/my/progress', courseController.getMyProgress)

export default router

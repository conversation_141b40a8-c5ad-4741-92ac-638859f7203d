{"name": "adaptive-elearning-server", "version": "1.0.0", "description": "Backend server for Adaptive E-Learning Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "redis": "^4.6.11", "ioredis": "^5.3.2", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-winston": "^4.2.0", "joi": "^17.11.0", "uuid": "^9.0.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "crypto": "^1.0.1", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0"}, "keywords": ["e-learning", "adaptive-learning", "education", "api", "nodejs", "express", "typescript"], "author": "Your Name", "license": "MIT"}
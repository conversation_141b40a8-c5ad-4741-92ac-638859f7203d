{"name": "habit-tracker-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "axios": "^1.6.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
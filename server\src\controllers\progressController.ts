import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export const progressController = {
  // Get user's overall progress
  async getMyProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const [enrollments, userStats, badges] = await Promise.all([
        prisma.enrollment.findMany({
          where: { userId },
          include: {
            course: {
              include: {
                _count: {
                  select: {
                    lessons: true,
                  },
                },
              },
            },
          },
        }),
        prisma.userStats.findUnique({
          where: { userId },
        }),
        prisma.userBadge.findMany({
          where: { userId },
          include: {
            badge: true,
          },
          orderBy: { earnedAt: 'desc' },
        }),
      ])

      // Calculate progress for each course
      const coursesWithProgress = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = enrollment.course._count.lessons
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            ...enrollment,
            progress: {
              completedLessons,
              totalLessons,
              progressPercentage: Math.round(progressPercentage),
              timeSpent: totalTimeSpent,
            },
          }
        })
      )

      // Calculate overall statistics
      const totalCoursesEnrolled = enrollments.length
      const completedCourses = coursesWithProgress.filter(c => c.progress.progressPercentage === 100).length
      const totalTimeSpent = coursesWithProgress.reduce((sum, c) => sum + c.progress.timeSpent, 0)
      const averageProgress = coursesWithProgress.length > 0
        ? coursesWithProgress.reduce((sum, c) => sum + c.progress.progressPercentage, 0) / coursesWithProgress.length
        : 0

      res.json({
        success: true,
        data: {
          overview: {
            totalCoursesEnrolled,
            completedCourses,
            totalTimeSpent,
            averageProgress: Math.round(averageProgress),
            streakDays: userStats?.streakDays || 0,
            totalPoints: userStats?.totalPoints || 0,
            level: userStats?.level || 1,
          },
          courses: coursesWithProgress,
          badges: badges.map(ub => ({
            ...ub.badge,
            earnedAt: ub.earnedAt,
          })),
        },
      })
    } catch (error) {
      logger.error('Get my progress error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get progress for a specific course
  async getMyCourseProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { courseId } = req.params

      // Check if user is enrolled
      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId,
            courseId,
          },
        },
        include: {
          course: {
            include: {
              lessons: {
                where: { isPublished: true },
                orderBy: { order: 'asc' },
              },
            },
          },
        },
      })

      if (!enrollment) {
        throw new AppError('Not enrolled in this course', 404)
      }

      // Get progress for each lesson
      const lessonsWithProgress = await Promise.all(
        enrollment.course.lessons.map(async (lesson) => {
          const progress = await prisma.progress.findUnique({
            where: {
              userId_lessonId: {
                userId,
                lessonId: lesson.id,
              },
            },
          })

          return {
            ...lesson,
            progress: progress ? {
              isCompleted: progress.isCompleted,
              timeSpent: progress.timeSpent,
              lastAccessed: progress.lastAccessed,
              completedAt: progress.completedAt,
            } : {
              isCompleted: false,
              timeSpent: 0,
              lastAccessed: null,
              completedAt: null,
            },
          }
        })
      )

      // Get quiz attempts for this course
      const quizAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId,
          quiz: {
            courseId,
          },
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              passingScore: true,
            },
          },
        },
        orderBy: { startedAt: 'desc' },
      })

      // Calculate overall course progress
      const completedLessons = lessonsWithProgress.filter(l => l.progress.isCompleted).length
      const totalLessons = lessonsWithProgress.length
      const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
      const totalTimeSpent = lessonsWithProgress.reduce((sum, l) => sum + l.progress.timeSpent, 0)

      res.json({
        success: true,
        data: {
          course: {
            id: enrollment.course.id,
            title: enrollment.course.title,
            description: enrollment.course.description,
            enrolledAt: enrollment.enrolledAt,
          },
          progress: {
            completedLessons,
            totalLessons,
            progressPercentage: Math.round(progressPercentage),
            timeSpent: totalTimeSpent,
          },
          lessons: lessonsWithProgress,
          quizAttempts: quizAttempts.map(attempt => ({
            ...attempt,
            passed: attempt.score >= attempt.quiz.passingScore,
          })),
        },
      })
    } catch (error) {
      logger.error('Get my course progress error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get user's learning statistics
  async getMyStats(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const [userStats, recentActivity, achievements] = await Promise.all([
        prisma.userStats.findUnique({
          where: { userId },
        }),
        this.getRecentActivity(userId),
        this.getAchievements(userId),
      ])

      // Calculate learning streak
      const streak = await this.calculateLearningStreak(userId)

      // Get learning time trends (last 30 days)
      const learningTrends = await this.getLearningTrends(userId)

      res.json({
        success: true,
        data: {
          stats: userStats || {
            coursesEnrolled: 0,
            coursesCompleted: 0,
            totalLearningTime: 0,
            streakDays: 0,
            averageScore: 0,
            totalPoints: 0,
            level: 1,
          },
          streak,
          recentActivity,
          achievements,
          learningTrends,
        },
      })
    } catch (error) {
      logger.error('Get my stats error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get user's achievements and badges
  async getMyAchievements(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const [userBadges, availableBadges] = await Promise.all([
        prisma.userBadge.findMany({
          where: { userId },
          include: {
            badge: true,
          },
          orderBy: { earnedAt: 'desc' },
        }),
        prisma.badge.findMany({
          orderBy: { name: 'asc' },
        }),
      ])

      const earnedBadgeIds = userBadges.map(ub => ub.badgeId)
      const unlockedBadges = userBadges.map(ub => ({
        ...ub.badge,
        earnedAt: ub.earnedAt,
      }))

      const lockedBadges = availableBadges
        .filter(badge => !earnedBadgeIds.includes(badge.id))
        .map(badge => ({
          ...badge,
          progress: this.calculateBadgeProgress(badge, userId),
        }))

      res.json({
        success: true,
        data: {
          unlocked: unlockedBadges,
          locked: lockedBadges,
          totalBadges: availableBadges.length,
          earnedBadges: unlockedBadges.length,
        },
      })
    } catch (error) {
      logger.error('Get my achievements error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course progress (for instructors)
  async getCourseProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params

      const [course, enrollments] = await Promise.all([
        prisma.course.findUnique({
          where: { id: courseId },
          include: {
            lessons: {
              where: { isPublished: true },
              orderBy: { order: 'asc' },
            },
          },
        }),
        prisma.enrollment.findMany({
          where: { courseId },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatar: true,
              },
            },
          },
        }),
      ])

      if (!course) {
        throw new AppError('Course not found', 404)
      }

      // Get progress for each student
      const studentsWithProgress = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId: enrollment.userId,
              lesson: {
                courseId,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = course.lessons.length
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)
          const lastActivity = progress.length > 0
            ? Math.max(...progress.map(p => p.lastAccessed.getTime()))
            : enrollment.enrolledAt.getTime()

          return {
            user: enrollment.user,
            enrollment: {
              enrolledAt: enrollment.enrolledAt,
              status: enrollment.status,
            },
            progress: {
              completedLessons,
              totalLessons,
              progressPercentage: Math.round(progressPercentage),
              timeSpent: totalTimeSpent,
              lastActivity: new Date(lastActivity),
            },
          }
        })
      )

      // Calculate course statistics
      const totalStudents = enrollments.length
      const activeStudents = studentsWithProgress.filter(s => {
        const daysSinceLastActivity = (Date.now() - s.progress.lastActivity.getTime()) / (1000 * 60 * 60 * 24)
        return daysSinceLastActivity <= 7
      }).length

      const averageProgress = studentsWithProgress.length > 0
        ? studentsWithProgress.reduce((sum, s) => sum + s.progress.progressPercentage, 0) / studentsWithProgress.length
        : 0

      const completionRate = studentsWithProgress.filter(s => s.progress.progressPercentage === 100).length / totalStudents * 100

      res.json({
        success: true,
        data: {
          course: {
            id: course.id,
            title: course.title,
            totalLessons: course.lessons.length,
          },
          statistics: {
            totalStudents,
            activeStudents,
            averageProgress: Math.round(averageProgress),
            completionRate: Math.round(completionRate),
          },
          students: studentsWithProgress.sort((a, b) => b.progress.progressPercentage - a.progress.progressPercentage),
        },
      })
    } catch (error) {
      logger.error('Get course progress error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get detailed student progress for a course
  async getCourseStudentProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          lessons: {
            where: { isPublished: true },
            orderBy: { order: 'asc' },
          },
        },
      })

      if (!course) {
        throw new AppError('Course not found', 404)
      }

      // Get progress for each lesson
      const lessonProgress = await Promise.all(
        course.lessons.map(async (lesson) => {
          const progress = await prisma.progress.findMany({
            where: { lessonId: lesson.id },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          })

          const completedCount = progress.filter(p => p.isCompleted).length
          const totalEnrolled = await prisma.enrollment.count({
            where: { courseId },
          })

          const averageTime = progress.length > 0
            ? progress.reduce((sum, p) => sum + p.timeSpent, 0) / progress.length
            : 0

          return {
            lesson: {
              id: lesson.id,
              title: lesson.title,
              order: lesson.order,
              duration: lesson.duration,
            },
            statistics: {
              completedCount,
              totalEnrolled,
              completionRate: Math.round((completedCount / totalEnrolled) * 100),
              averageTime: Math.round(averageTime),
            },
            studentProgress: progress.map(p => ({
              user: p.user,
              isCompleted: p.isCompleted,
              timeSpent: p.timeSpent,
              lastAccessed: p.lastAccessed,
              completedAt: p.completedAt,
            })),
          }
        })
      )

      res.json({
        success: true,
        data: {
          course: {
            id: course.id,
            title: course.title,
          },
          lessons: lessonProgress,
        },
      })
    } catch (error) {
      logger.error('Get course student progress error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get course progress analytics
  async getCourseProgressAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params

      const [enrollmentTrends, completionTrends, engagementMetrics] = await Promise.all([
        this.getEnrollmentTrends(courseId),
        this.getCompletionTrends(courseId),
        this.getEngagementMetrics(courseId),
      ])

      res.json({
        success: true,
        data: {
          enrollmentTrends,
          completionTrends,
          engagementMetrics,
        },
      })
    } catch (error) {
      logger.error('Get course progress analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get user progress (for admins)
  async getUserProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { userId } = req.params

      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          stats: true,
          enrollments: {
            include: {
              course: {
                select: {
                  id: true,
                  title: true,
                  category: true,
                },
              },
            },
          },
        },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Get detailed progress for each course
      const coursesWithProgress = await Promise.all(
        user.enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const totalLessons = await prisma.lesson.count({
            where: {
              courseId: enrollment.courseId,
              isPublished: true,
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            ...enrollment,
            progress: {
              completedLessons,
              totalLessons,
              progressPercentage: Math.round(progressPercentage),
              timeSpent: totalTimeSpent,
            },
          }
        })
      )

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            role: user.role,
          },
          stats: user.stats,
          courses: coursesWithProgress,
        },
      })
    } catch (error) {
      logger.error('Get user progress error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get user course progress (for admins)
  async getUserCourseProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { userId } = req.params

      const enrollments = await prisma.enrollment.findMany({
        where: { userId },
        include: {
          course: {
            include: {
              _count: {
                select: {
                  lessons: true,
                },
              },
            },
          },
        },
      })

      const coursesWithProgress = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = enrollment.course._count.lessons
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            ...enrollment,
            progress: {
              completedLessons,
              totalLessons,
              progressPercentage: Math.round(progressPercentage),
              timeSpent: totalTimeSpent,
            },
          }
        })
      )

      res.json({
        success: true,
        data: coursesWithProgress,
      })
    } catch (error) {
      logger.error('Get user course progress error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get progress overview (for admins)
  async getProgressOverview(req: AuthenticatedRequest, res: Response) {
    try {
      const [totalUsers, totalCourses, totalEnrollments, completionStats] = await Promise.all([
        prisma.user.count({ where: { role: 'STUDENT' } }),
        prisma.course.count({ where: { isPublished: true } }),
        prisma.enrollment.count(),
        this.getCompletionStats(),
      ])

      const averageProgress = await this.getAverageProgress()
      const engagementMetrics = await this.getOverallEngagementMetrics()

      res.json({
        success: true,
        data: {
          overview: {
            totalUsers,
            totalCourses,
            totalEnrollments,
            averageProgress: Math.round(averageProgress),
          },
          completionStats,
          engagementMetrics,
        },
      })
    } catch (error) {
      logger.error('Get progress overview error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get progress trends (for admins)
  async getProgressTrends(req: AuthenticatedRequest, res: Response) {
    try {
      const { period = '30' } = req.query
      const days = parseInt(period as string)

      const trends = await this.getProgressTrendsData(days)

      res.json({
        success: true,
        data: trends,
      })
    } catch (error) {
      logger.error('Get progress trends error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Helper methods
  async getRecentActivity(userId: string) {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const recentProgress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: sevenDaysAgo },
      },
      include: {
        lesson: {
          include: {
            course: {
              select: {
                title: true,
              },
            },
          },
        },
      },
      orderBy: { lastAccessed: 'desc' },
      take: 10,
    })

    return recentProgress.map(p => ({
      type: 'lesson_progress',
      lessonTitle: p.lesson.title,
      courseTitle: p.lesson.course.title,
      timeSpent: p.timeSpent,
      isCompleted: p.isCompleted,
      timestamp: p.lastAccessed,
    }))
  },

  async getAchievements(userId: string) {
    const userBadges = await prisma.userBadge.findMany({
      where: { userId },
      include: {
        badge: true,
      },
      orderBy: { earnedAt: 'desc' },
      take: 5,
    })

    return userBadges.map(ub => ({
      ...ub.badge,
      earnedAt: ub.earnedAt,
    }))
  },

  async calculateLearningStreak(userId: string) {
    // Calculate consecutive days of learning activity
    const progress = await prisma.progress.findMany({
      where: { userId },
      select: { lastAccessed: true },
      orderBy: { lastAccessed: 'desc' },
    })

    if (progress.length === 0) return { current: 0, longest: 0 }

    const dates = progress.map(p => p.lastAccessed.toDateString())
    const uniqueDates = [...new Set(dates)].sort((a, b) => new Date(b).getTime() - new Date(a).getTime())

    let currentStreak = 0
    let longestStreak = 0
    let tempStreak = 0

    const today = new Date().toDateString()
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()

    // Check if streak is still active
    if (uniqueDates[0] === today || uniqueDates[0] === yesterday) {
      currentStreak = 1
      tempStreak = 1

      for (let i = 1; i < uniqueDates.length; i++) {
        const currentDate = new Date(uniqueDates[i])
        const previousDate = new Date(uniqueDates[i - 1])
        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)

        if (dayDiff === 1) {
          currentStreak++
          tempStreak++
        } else {
          break
        }
      }
    }

    // Calculate longest streak
    tempStreak = 1
    for (let i = 1; i < uniqueDates.length; i++) {
      const currentDate = new Date(uniqueDates[i])
      const previousDate = new Date(uniqueDates[i - 1])
      const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)

      if (dayDiff === 1) {
        tempStreak++
      } else {
        longestStreak = Math.max(longestStreak, tempStreak)
        tempStreak = 1
      }
    }
    longestStreak = Math.max(longestStreak, tempStreak)

    return { current: currentStreak, longest: longestStreak }
  },

  async getLearningTrends(userId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const progress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: thirtyDaysAgo },
      },
      select: {
        timeSpent: true,
        lastAccessed: true,
      },
    })

    // Group by day and calculate daily learning time
    const dailyTime = {}
    progress.forEach(p => {
      const day = p.lastAccessed.toDateString()
      dailyTime[day] = (dailyTime[day] || 0) + p.timeSpent
    })

    const trends = Object.entries(dailyTime)
      .map(([date, time]) => ({
        date,
        timeSpent: time,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    return trends
  },

  calculateBadgeProgress(badge: any, userId: string) {
    // Calculate progress towards earning a badge
    // This would depend on the badge criteria
    return Math.floor(Math.random() * 100) // Placeholder
  },

  async getEnrollmentTrends(courseId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const enrollments = await prisma.enrollment.findMany({
      where: {
        courseId,
        enrolledAt: { gte: thirtyDaysAgo },
      },
      select: { enrolledAt: true },
    })

    // Group by day
    const dailyEnrollments = {}
    enrollments.forEach(e => {
      const day = e.enrolledAt.toDateString()
      dailyEnrollments[day] = (dailyEnrollments[day] || 0) + 1
    })

    return Object.entries(dailyEnrollments).map(([date, count]) => ({
      date,
      enrollments: count,
    }))
  },

  async getCompletionTrends(courseId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const completions = await prisma.progress.findMany({
      where: {
        lesson: { courseId },
        isCompleted: true,
        completedAt: { gte: thirtyDaysAgo },
      },
      select: { completedAt: true },
    })

    // Group by day
    const dailyCompletions = {}
    completions.forEach(c => {
      if (c.completedAt) {
        const day = c.completedAt.toDateString()
        dailyCompletions[day] = (dailyCompletions[day] || 0) + 1
      }
    })

    return Object.entries(dailyCompletions).map(([date, count]) => ({
      date,
      completions: count,
    }))
  },

  async getEngagementMetrics(courseId: string) {
    const enrollments = await prisma.enrollment.findMany({
      where: { courseId },
    })

    const totalStudents = enrollments.length
    const activeStudents = await prisma.progress.groupBy({
      by: ['userId'],
      where: {
        lesson: { courseId },
        lastAccessed: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
    })

    const averageTimeSpent = await prisma.progress.aggregate({
      where: {
        lesson: { courseId },
      },
      _avg: { timeSpent: true },
    })

    return {
      totalStudents,
      activeStudents: activeStudents.length,
      engagementRate: totalStudents > 0 ? (activeStudents.length / totalStudents) * 100 : 0,
      averageTimeSpent: averageTimeSpent._avg.timeSpent || 0,
    }
  },

  async getCompletionStats() {
    const [totalEnrollments, completedEnrollments] = await Promise.all([
      prisma.enrollment.count(),
      prisma.enrollment.count({
        where: { status: 'COMPLETED' },
      }),
    ])

    const completionRate = totalEnrollments > 0 ? (completedEnrollments / totalEnrollments) * 100 : 0

    return {
      totalEnrollments,
      completedEnrollments,
      completionRate: Math.round(completionRate),
    }
  },

  async getAverageProgress() {
    const enrollments = await prisma.enrollment.findMany({
      select: { progress: true },
    })

    if (enrollments.length === 0) return 0

    const totalProgress = enrollments.reduce((sum, e) => sum + e.progress, 0)
    return totalProgress / enrollments.length
  },

  async getOverallEngagementMetrics() {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const [totalUsers, activeUsers, totalTime] = await Promise.all([
      prisma.user.count({ where: { role: 'STUDENT' } }),
      prisma.progress.groupBy({
        by: ['userId'],
        where: {
          lastAccessed: { gte: sevenDaysAgo },
        },
      }),
      prisma.progress.aggregate({
        where: {
          lastAccessed: { gte: sevenDaysAgo },
        },
        _sum: { timeSpent: true },
      }),
    ])

    return {
      totalUsers,
      activeUsers: activeUsers.length,
      engagementRate: totalUsers > 0 ? (activeUsers.length / totalUsers) * 100 : 0,
      totalLearningTime: totalTime._sum.timeSpent || 0,
    }
  },

  async getProgressTrendsData(days: number) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const progress = await prisma.progress.findMany({
      where: {
        lastAccessed: { gte: startDate },
      },
      select: {
        lastAccessed: true,
        timeSpent: true,
        isCompleted: true,
      },
    })

    // Group by day
    const dailyData = {}
    progress.forEach(p => {
      const day = p.lastAccessed.toDateString()
      if (!dailyData[day]) {
        dailyData[day] = {
          date: day,
          timeSpent: 0,
          completions: 0,
          sessions: 0,
        }
      }
      dailyData[day].timeSpent += p.timeSpent
      dailyData[day].sessions += 1
      if (p.isCompleted) {
        dailyData[day].completions += 1
      }
    })

    return Object.values(dailyData).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    )
  },
}

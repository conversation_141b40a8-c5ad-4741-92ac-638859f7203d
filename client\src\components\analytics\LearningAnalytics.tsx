'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Clock, 
  TrendingUp, 
  Target, 
  Brain,
  BarChart3,
  <PERSON><PERSON>hart,
  Calendar,
  Award
} from 'lucide-react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  <PERSON><PERSON>hart, 
  Bar,
  <PERSON><PERSON> as RechartsPie<PERSON>hart,
  Cell,
  Area,
  AreaChart
} from 'recharts'

interface LearningAnalyticsData {
  learningTime: {
    period: string
    dailyData: Array<{
      date: string
      timeSpent: number
    }>
    categoryBreakdown: Array<{
      category: string
      time: number
    }>
    summary: {
      totalTime: number
      dailyAverage: number
      activeDays: number
    }
  }
  performance: {
    quizPerformance: {
      totalAttempts: number
      averageScore: number
      recentTrend: number[]
    }
    courseProgress: Array<{
      courseTitle: string
      category: string
      progress: number
      status: string
      enrolledAt: string
    }>
    skillAssessment: Record<string, number>
    improvementAreas: Array<{
      area: string
      score: number
      priority: string
    }>
    strengths: Array<{
      area: string
      score: number
      confidence: string
    }>
  }
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

export default function LearningAnalytics() {
  const [analyticsData, setAnalyticsData] = useState<LearningAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timePeriod, setTimePeriod] = useState('30')

  useEffect(() => {
    fetchAnalyticsData()
  }, [timePeriod])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      const [timeResponse, performanceResponse] = await Promise.all([
        fetch(`/api/analytics/my/learning-time?period=${timePeriod}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }),
        fetch('/api/analytics/my/performance', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        })
      ])
      
      if (timeResponse.ok && performanceResponse.ok) {
        const [timeData, performanceData] = await Promise.all([
          timeResponse.json(),
          performanceResponse.json()
        ])
        
        setAnalyticsData({
          learningTime: timeData.data,
          performance: performanceData.data,
        })
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load analytics data</p>
      </div>
    )
  }

  const { learningTime, performance } = analyticsData

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Learning Analytics</h1>
          <p className="text-muted-foreground">Track your learning progress and performance</p>
        </div>
        <Select value={timePeriod} onValueChange={setTimePeriod}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Learning Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(learningTime.summary.totalTime / 60)}h</div>
            <p className="text-xs text-muted-foreground">
              {learningTime.summary.dailyAverage} min/day average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Days</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningTime.summary.activeDays}</div>
            <p className="text-xs text-muted-foreground">
              out of {parseInt(timePeriod)} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quiz Average</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performance.quizPerformance.averageScore}%</div>
            <p className="text-xs text-muted-foreground">
              {performance.quizPerformance.totalAttempts} attempts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Courses in Progress</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performance.courseProgress.filter(c => c.status === 'ACTIVE').length}
            </div>
            <p className="text-xs text-muted-foreground">
              {performance.courseProgress.filter(c => c.status === 'COMPLETED').length} completed
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="time" className="space-y-4">
        <TabsList>
          <TabsTrigger value="time">Learning Time</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="progress">Course Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="time" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Daily Learning Time */}
            <Card>
              <CardHeader>
                <CardTitle>Daily Learning Time</CardTitle>
                <CardDescription>
                  Your learning activity over the selected period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={learningTime.dailyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="timeSpent" 
                      stroke="#3b82f6" 
                      fill="#3b82f6"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Time by Category</CardTitle>
                <CardDescription>
                  How you spend your learning time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={learningTime.categoryBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="time"
                    >
                      {learningTime.categoryBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quiz Performance Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Quiz Performance Trend</CardTitle>
                <CardDescription>
                  Your recent quiz scores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performance.quizPerformance.recentTrend.map((score, index) => ({
                    attempt: index + 1,
                    score
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="attempt" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="score" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={{ fill: '#10b981' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Improvement Areas */}
            <Card>
              <CardHeader>
                <CardTitle>Areas for Improvement</CardTitle>
                <CardDescription>
                  Focus areas to enhance your learning
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performance.improvementAreas.map((area, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{area.area}</span>
                        <Badge variant={area.priority === 'high' ? 'destructive' : 'secondary'}>
                          {area.priority}
                        </Badge>
                      </div>
                      <Progress value={area.score} className="h-2" />
                      <p className="text-xs text-muted-foreground">{area.score}% proficiency</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Strengths */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Your Strengths
              </CardTitle>
              <CardDescription>
                Areas where you excel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {performance.strengths.map((strength, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">{strength.area}</h3>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {strength.confidence}
                      </Badge>
                    </div>
                    <Progress value={strength.score} className="h-2 mb-2" />
                    <p className="text-sm text-muted-foreground">{strength.score}% proficiency</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Skill Assessment
              </CardTitle>
              <CardDescription>
                Your current skill levels across different areas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(performance.skillAssessment).map(([skill, level]) => (
                  <div key={skill} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium capitalize">{skill}</span>
                      <span className="text-sm text-muted-foreground">{level}%</span>
                    </div>
                    <Progress value={level} className="h-3" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Beginner</span>
                      <span>Intermediate</span>
                      <span>Advanced</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Progress</CardTitle>
              <CardDescription>
                Your progress across all enrolled courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performance.courseProgress.map((course, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium">{course.courseTitle}</h3>
                        <p className="text-sm text-muted-foreground">{course.category}</p>
                      </div>
                      <Badge variant={course.status === 'COMPLETED' ? 'default' : 'secondary'}>
                        {course.status}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{course.progress}%</span>
                      </div>
                      <Progress value={course.progress} className="h-2" />
                      <p className="text-xs text-muted-foreground">
                        Enrolled on {new Date(course.enrolledAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

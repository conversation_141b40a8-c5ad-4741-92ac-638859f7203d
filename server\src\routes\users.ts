import { Router } from 'express'
import { userController } from '@/controllers/userController'
import { authorize } from '@/middleware/auth'

const router = Router()

// User management routes (admin only)
router.get('/', authorize('ADMIN'), userController.getUsers)
router.get('/:id', authorize('ADMIN'), userController.getUserById)
router.put('/:id', authorize('ADMIN'), userController.updateUser)
router.delete('/:id', authorize('ADMIN'), userController.deleteUser)
router.post('/:id/activate', authorize('ADMIN'), userController.activateUser)
router.post('/:id/deactivate', authorize('ADMIN'), userController.deactivateUser)

// User statistics
router.get('/:id/stats', authorize('ADMIN'), userController.getUserStats)
router.get('/stats/overview', authorize('ADMIN'), userController.getUsersOverview)

export default router

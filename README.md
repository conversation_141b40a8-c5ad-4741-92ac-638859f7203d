# Adaptive E-Learning Platform

A comprehensive, modern e-learning platform with adaptive learning capabilities, built with Next.js, Express.js, and PostgreSQL.

## 🚀 Features

### Core Features
- **User Authentication & Authorization** - JWT-based auth with role-based access control
- **Course Management** - Create, manage, and publish courses with multimedia content
- **Adaptive Learning Engine** - Personalized learning paths based on user performance
- **Student Progress Tracking** - Comprehensive analytics and progress monitoring
- **Interactive Learning Components** - Quizzes, assignments, and multimedia lessons
- **Real-time Communication** - Live discussions and notifications via Socket.IO
- **Dashboard & Analytics** - Detailed insights for students, instructors, and admins

### Advanced Features
- **Adaptive Content Delivery** - AI-powered content recommendations
- **Learning Analytics** - Performance tracking and skill assessment
- **Gamification** - Badges, achievements, and progress rewards
- **Multi-role Support** - Students, Instructors, and Administrators
- **Responsive Design** - Mobile-first, accessible interface
- **File Upload & Management** - Course materials and media handling

## 🛠 Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Modern UI component library
- **Recharts** - Data visualization
- **Socket.IO Client** - Real-time communication

### Backend
- **Express.js** - Node.js web framework
- **TypeScript** - Type-safe server development
- **Prisma** - Database ORM and migrations
- **PostgreSQL** - Primary database
- **Redis** - Caching and session management
- **Socket.IO** - Real-time communication
- **JWT** - Authentication tokens
- **Bcrypt** - Password hashing
- **Multer** - File upload handling

### DevOps & Tools
- **Docker** - Containerization
- **ESLint & Prettier** - Code formatting and linting
- **Husky** - Git hooks
- **Jest** - Testing framework

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- Redis 6+
- npm or yarn

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd adaptive-elearning-platform
```

### 2. Install Dependencies
```bash
# Install all dependencies (client + server)
npm run install:all

# Or install separately
cd client && npm install
cd ../server && npm install
```

### 3. Environment Setup

#### Server Environment (.env)
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/elearning_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Email Configuration
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT="587"
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# File Upload
MAX_FILE_SIZE="10485760"
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Client URL
CLIENT_URL="http://localhost:3000"

# Environment
NODE_ENV="development"
PORT="5000"
```

#### Client Environment (.env.local)
```env
NEXT_PUBLIC_API_URL="http://localhost:5000/api"
NEXT_PUBLIC_SOCKET_URL="http://localhost:5000"
```

### 4. Database Setup
```bash
cd server

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Seed the database (optional)
npx prisma db seed
```

### 5. Start Development Servers
```bash
# Start both client and server
npm run dev

# Or start separately
npm run dev:client  # Starts Next.js on port 3000
npm run dev:server  # Starts Express on port 5000
```

### 6. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Database Studio**: `npx prisma studio` (port 5555)

## 📁 Project Structure

```
adaptive-elearning-platform/
├── client/                 # Next.js frontend
│   ├── src/
│   │   ├── app/           # App router pages
│   │   ├── components/    # Reusable components
│   │   ├── lib/          # Utilities and configurations
│   │   └── types/        # TypeScript type definitions
│   ├── public/           # Static assets
│   └── package.json
├── server/                # Express.js backend
│   ├── src/
│   │   ├── controllers/  # Route controllers
│   │   ├── middleware/   # Custom middleware
│   │   ├── routes/       # API routes
│   │   ├── services/     # Business logic
│   │   ├── utils/        # Utility functions
│   │   ├── config/       # Configuration files
│   │   └── types/        # TypeScript types
│   ├── prisma/           # Database schema and migrations
│   └── package.json
├── docs/                 # Documentation
├── docker-compose.yml    # Docker configuration
└── package.json         # Root package.json
```

## 🔧 Available Scripts

### Root Level
```bash
npm run install:all    # Install all dependencies
npm run dev           # Start both client and server
npm run build         # Build both applications
npm run start         # Start production servers
npm run lint          # Lint both applications
npm run test          # Run all tests
```

### Client Scripts
```bash
npm run dev:client    # Start Next.js dev server
npm run build:client # Build Next.js application
npm run start:client # Start Next.js production server
npm run lint:client  # Lint client code
```

### Server Scripts
```bash
npm run dev:server    # Start Express dev server
npm run build:server # Build Express application
npm run start:server # Start Express production server
npm run lint:server  # Lint server code
npm run db:migrate    # Run database migrations
npm run db:seed      # Seed database
npm run db:studio    # Open Prisma Studio
```

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run client tests
cd client && npm test

# Run server tests
cd server && npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 🐳 Docker Deployment

### Development with Docker
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### Course Management
- `GET /api/courses` - Get all courses
- `POST /api/courses` - Create new course (instructor)
- `GET /api/courses/:id` - Get course details
- `PUT /api/courses/:id` - Update course (instructor)
- `DELETE /api/courses/:id` - Delete course (instructor)
- `POST /api/courses/:id/enroll` - Enroll in course

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/progress` - Get user progress
- `GET /api/users/achievements` - Get user achievements

### Analytics
- `GET /api/analytics/my/dashboard` - Student dashboard data
- `GET /api/analytics/instructor/dashboard` - Instructor dashboard
- `GET /api/analytics/admin/dashboard` - Admin dashboard

## 🔐 Security Features

- **JWT Authentication** with refresh tokens
- **Role-based Access Control** (RBAC)
- **Password Hashing** with bcrypt
- **Input Validation** and sanitization
- **Rate Limiting** on API endpoints
- **CORS Configuration** for cross-origin requests
- **Helmet.js** for security headers
- **SQL Injection Protection** via Prisma ORM

## 🎯 User Roles & Permissions

### Student
- Enroll in courses
- Access course content
- Take quizzes and assignments
- Track learning progress
- View personalized recommendations

### Instructor
- Create and manage courses
- Upload course materials
- Create quizzes and assignments
- Monitor student progress
- Access course analytics

### Administrator
- Manage all users and courses
- Access platform analytics
- Configure system settings
- Monitor platform health

## 🚀 Deployment

### Environment Variables for Production
Ensure all environment variables are properly set for production deployment.

### Database Migration
```bash
# Production migration
npx prisma migrate deploy
```

### Build and Start
```bash
# Build applications
npm run build

# Start production servers
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `/docs` folder
- Review the API documentation

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for a list of changes and version history.

---

Built with ❤️ using modern web technologies

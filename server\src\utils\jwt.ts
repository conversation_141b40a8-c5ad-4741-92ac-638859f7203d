import jwt from 'jsonwebtoken'
import { AppError } from './AppError'

export interface TokenPayload {
  userId: string
  type?: string
}

export const generateTokens = (userId: string, rememberMe: boolean = false) => {
  const accessTokenExpiry = process.env.JWT_EXPIRES_IN || '15m'
  const refreshTokenExpiry = rememberMe ? '30d' : (process.env.JWT_REFRESH_EXPIRES_IN || '7d')

  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET!,
    { expiresIn: accessTokenExpiry }
  )

  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET!,
    { expiresIn: refreshTokenExpiry }
  )

  return { accessToken, refreshToken }
}

export const verifyAccessToken = (token: string): TokenPayload => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!) as TokenPayload
  } catch (error) {
    throw new AppError('Invalid access token', 401)
  }
}

export const verifyRefreshToken = (token: string): string => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET!) as TokenPayload
    if (decoded.type !== 'refresh') {
      throw new AppError('Invalid token type', 401)
    }
    return decoded.userId
  } catch (error) {
    throw new AppError('Invalid refresh token', 401)
  }
}

export const generateEmailVerificationToken = (userId: string): string => {
  return jwt.sign(
    { userId, type: 'email_verification' },
    process.env.JWT_SECRET!,
    { expiresIn: '24h' }
  )
}

export const verifyEmailVerificationToken = (token: string): string => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as TokenPayload
    if (decoded.type !== 'email_verification') {
      throw new AppError('Invalid token type', 400)
    }
    return decoded.userId
  } catch (error) {
    throw new AppError('Invalid or expired verification token', 400)
  }
}

# Setup Guide - Adaptive E-Learning Platform

## 🚀 Option 1: Quick Start with Docker (Recommended)

### Step 1: Clone and Navigate
```bash
cd "c:\Users\<USER>\Documents\augment-projects\Habit tracker"
```

### Step 2: Start with Docker
```bash
# Start all services (PostgreSQL, Redis, Server, Client)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Step 3: Setup Database
```bash
# Run database migrations
docker-compose exec server npx prisma migrate dev

# Seed the database with sample data
docker-compose exec server npx prisma db seed
```

### Step 4: Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Database Studio**: Run `docker-compose exec server npx prisma studio` then visit http://localhost:5555

---

## 🛠 Option 2: Manual Setup (Local Development)

### Step 1: Install Dependencies
```bash
# Install all dependencies
npm run install:all
```

### Step 2: Setup PostgreSQL
```bash
# Option A: Install PostgreSQL locally
# Download from: https://www.postgresql.org/download/

# Option B: Use Docker for just the database
docker run --name elearning_postgres -e POSTGRES_DB=elearning_db -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password123 -p 5432:5432 -d postgres:15
```

### Step 3: Setup Redis
```bash
# Option A: Install Redis locally
# Download from: https://redis.io/download

# Option B: Use Docker for Redis
docker run --name elearning_redis -p 6379:6379 -d redis:7-alpine
```

### Step 4: Environment Configuration

#### Server Environment (.env)
Create `server/.env`:
```env
# Database
DATABASE_URL="postgresql://postgres:password123@localhost:5432/elearning_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT Secrets (Change these in production!)
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Email Configuration (Optional - for password reset)
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT="587"
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# File Upload
MAX_FILE_SIZE="10485760"
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Client URL
CLIENT_URL="http://localhost:3000"

# Environment
NODE_ENV="development"
PORT="5000"
```

#### Client Environment (.env.local)
Create `client/.env.local`:
```env
NEXT_PUBLIC_API_URL="http://localhost:5000/api"
NEXT_PUBLIC_SOCKET_URL="http://localhost:5000"
```

### Step 5: Database Setup
```bash
cd server

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Seed the database (optional)
npx prisma db seed
```

### Step 6: Start Development Servers
```bash
# Start both client and server
npm run dev

# Or start separately in different terminals:
npm run dev:server  # Terminal 1 - Starts Express on port 5000
npm run dev:client  # Terminal 2 - Starts Next.js on port 3000
```

---

## 🔧 Troubleshooting

### Common Issues:

1. **Port Already in Use**
   ```bash
   # Kill processes on ports
   npx kill-port 3000 5000
   ```

2. **Database Connection Error**
   ```bash
   # Check if PostgreSQL is running
   docker ps  # If using Docker
   # Or check local PostgreSQL service
   ```

3. **Redis Connection Error**
   ```bash
   # Check if Redis is running
   docker ps  # If using Docker
   # Or check local Redis service
   ```

4. **Prisma Client Error**
   ```bash
   cd server
   npx prisma generate
   npx prisma migrate reset  # This will reset and reseed the database
   ```

5. **Node Modules Issues**
   ```bash
   # Clean install
   rm -rf node_modules package-lock.json
   rm -rf client/node_modules client/package-lock.json
   rm -rf server/node_modules server/package-lock.json
   npm run install:all
   ```

---

## 📱 Default Login Credentials

After seeding the database, you can use these test accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

### Instructor Account
- **Email**: <EMAIL>
- **Password**: instructor123

### Student Account
- **Email**: <EMAIL>
- **Password**: student123

---

## 🧪 Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

---

## 🚀 Production Deployment

### Environment Variables for Production
Make sure to change these in production:
- `JWT_SECRET` and `JWT_REFRESH_SECRET` - Use strong, unique secrets
- Database credentials
- Email service credentials
- Cloudinary credentials (for file uploads)

### Build and Deploy
```bash
# Build applications
npm run build

# Start production servers
npm run start
```

---

## 📊 Database Management

```bash
# Open Prisma Studio (Database GUI)
cd server && npx prisma studio

# Reset database
cd server && npx prisma migrate reset

# Deploy migrations (production)
cd server && npx prisma migrate deploy
```

---

## 🆘 Need Help?

1. Check the logs:
   ```bash
   # Docker logs
   docker-compose logs -f

   # Or check individual service logs
   docker-compose logs server
   docker-compose logs client
   ```

2. Verify services are running:
   ```bash
   # Check Docker containers
   docker ps

   # Check local processes
   netstat -an | findstr :3000
   netstat -an | findstr :5000
   ```

3. Test API endpoints:
   ```bash
   # Test server health
   curl http://localhost:5000/api/health
   ```

---

## 🎯 Next Steps

Once everything is running:

1. **Visit the Application**: http://localhost:3000
2. **Create an Account** or use the default credentials above
3. **Explore Features**:
   - Create courses (as instructor)
   - Enroll in courses (as student)
   - Take quizzes and track progress
   - View analytics and dashboards
4. **Check the API**: http://localhost:5000/api
5. **Explore the Database**: Run Prisma Studio

The platform is now ready for development and testing! 🎉

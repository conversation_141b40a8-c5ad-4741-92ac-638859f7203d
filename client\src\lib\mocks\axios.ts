// Mock implementation of axios for development until the real package is installed
import { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from '../../types/axios'

class MockAxiosError extends Error implements AxiosError {
  config: AxiosRequestConfig
  code?: string
  request?: any
  response?: AxiosResponse

  constructor(message: string, config: AxiosRequestConfig, code?: string, request?: any, response?: AxiosResponse) {
    super(message)
    this.name = 'AxiosError'
    this.config = config
    this.code = code
    this.request = request
    this.response = response
  }
}

const createMockAxiosInstance = (config: AxiosRequestConfig = {}): AxiosInstance => {
  const instance = async (configOrUrl: AxiosRequestConfig | string, config?: AxiosRequestConfig): Promise<AxiosResponse> => {
    console.warn('Mock axios call - install axios package for real functionality')
    
    const finalConfig = typeof configOrUrl === 'string' 
      ? { ...config, url: configOrUrl }
      : configOrUrl

    // Simulate a successful response
    return {
      data: { message: 'Mock response' },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: finalConfig
    }
  }

  instance.defaults = config
  instance.interceptors = {
    request: {
      use: (onFulfilled?: any, onRejected?: any) => {
        console.warn('Mock axios request interceptor')
        return 0
      }
    },
    response: {
      use: (onFulfilled?: any, onRejected?: any) => {
        console.warn('Mock axios response interceptor')
        return 0
      }
    }
  }

  instance.get = async (url: string, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'GET' })
  instance.post = async (url: string, data?: any, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'POST', data })
  instance.put = async (url: string, data?: any, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'PUT', data })
  instance.patch = async (url: string, data?: any, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'PATCH', data })
  instance.delete = async (url: string, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'DELETE' })
  instance.head = async (url: string, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'HEAD' })
  instance.options = async (url: string, config?: AxiosRequestConfig) => instance(url, { ...config, method: 'OPTIONS' })

  return instance
}

const mockAxios = createMockAxiosInstance()
mockAxios.create = createMockAxiosInstance
mockAxios.Cancel = class MockCancel {}
mockAxios.CancelToken = class MockCancelToken {}
mockAxios.isCancel = () => false
mockAxios.all = Promise.all.bind(Promise)
mockAxios.spread = (callback: any) => (array: any[]) => callback(...array)
mockAxios.isAxiosError = (payload: any): payload is AxiosError => payload instanceof MockAxiosError

export default mockAxios
export { MockAxiosError as AxiosError }
export type { AxiosInstance, AxiosRequestConfig, AxiosResponse }

import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export const quizController = {
  // Get quiz by ID
  async getQuizById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructorId: true,
            },
          },
          lesson: {
            select: {
              id: true,
              title: true,
            },
          },
          questions: {
            orderBy: { order: 'asc' },
          },
          attempts: req.user ? {
            where: { userId: req.user.id },
            orderBy: { startedAt: 'desc' },
            take: 1,
          } : false,
        },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      // Check access permissions
      if (req.user) {
        const hasAccess = await this.checkQuizAccess(req.user.id, quiz)
        if (!hasAccess) {
          throw new AppError('Access denied to this quiz', 403)
        }
      }

      // Hide correct answers for students
      const isInstructor = quiz.course?.instructorId === req.user?.id || req.user?.role === 'ADMIN'
      const questionsForUser = quiz.questions.map(question => ({
        ...question,
        correctAnswer: isInstructor ? question.correctAnswer : undefined,
        explanation: isInstructor ? question.explanation : undefined,
      }))

      const quizData = {
        ...quiz,
        questions: questionsForUser,
        userAttempt: quiz.attempts?.[0] || null,
        attempts: undefined,
      }

      res.json({
        success: true,
        data: quizData,
      })
    } catch (error) {
      logger.error('Get quiz by ID error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Start quiz attempt
  async startQuizAttempt(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: {
          course: true,
          questions: {
            orderBy: { order: 'asc' },
            select: {
              id: true,
              question: true,
              type: true,
              options: true,
              points: true,
              order: true,
            },
          },
        },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      // Check access permissions
      const hasAccess = await this.checkQuizAccess(req.user!.id, quiz)
      if (!hasAccess) {
        throw new AppError('Access denied to this quiz', 403)
      }

      // Check if user has already started this quiz
      const existingAttempt = await prisma.quizAttempt.findFirst({
        where: {
          userId: req.user!.id,
          quizId: id,
          completedAt: null,
        },
      })

      if (existingAttempt) {
        throw new AppError('Quiz attempt already in progress', 400)
      }

      // Create new attempt
      const attempt = await prisma.quizAttempt.create({
        data: {
          userId: req.user!.id,
          quizId: id,
          answers: {},
          score: 0,
          timeSpent: 0,
        },
      })

      res.status(201).json({
        success: true,
        message: 'Quiz attempt started',
        data: {
          attemptId: attempt.id,
          quiz: {
            id: quiz.id,
            title: quiz.title,
            description: quiz.description,
            timeLimit: quiz.timeLimit,
            questions: quiz.questions,
          },
          startedAt: attempt.startedAt,
        },
      })
    } catch (error) {
      logger.error('Start quiz attempt error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Submit quiz attempt
  async submitQuizAttempt(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { answers } = req.body

      const attempt = await prisma.quizAttempt.findFirst({
        where: {
          userId: req.user!.id,
          quizId: id,
          completedAt: null,
        },
        include: {
          quiz: {
            include: {
              questions: true,
            },
          },
        },
      })

      if (!attempt) {
        throw new AppError('No active quiz attempt found', 404)
      }

      // Calculate score
      const { score, results } = this.calculateQuizScore(attempt.quiz.questions, answers)

      // Calculate time spent
      const timeSpent = Math.round((Date.now() - attempt.startedAt.getTime()) / 1000 / 60) // in minutes

      // Update attempt
      const completedAttempt = await prisma.quizAttempt.update({
        where: { id: attempt.id },
        data: {
          answers,
          score,
          timeSpent,
          completedAt: new Date(),
        },
      })

      // Check if passed
      const passed = score >= attempt.quiz.passingScore

      // Update user stats if passed
      if (passed) {
        await this.updateUserStats(req.user!.id, score)
      }

      res.json({
        success: true,
        message: 'Quiz submitted successfully',
        data: {
          attemptId: completedAttempt.id,
          score,
          passed,
          passingScore: attempt.quiz.passingScore,
          timeSpent,
          results,
        },
      })
    } catch (error) {
      logger.error('Submit quiz attempt error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get user's quiz attempts
  async getMyQuizAttempts(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId: req.user!.id,
          quizId: id,
        },
        orderBy: { startedAt: 'desc' },
      })

      res.json({
        success: true,
        data: attempts,
      })
    } catch (error) {
      logger.error('Get my quiz attempts error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get quiz results
  async getQuizResults(req: AuthenticatedRequest, res: Response) {
    try {
      const { id, attemptId } = req.params

      const attempt = await prisma.quizAttempt.findUnique({
        where: { id: attemptId },
        include: {
          quiz: {
            include: {
              questions: {
                orderBy: { order: 'asc' },
              },
            },
          },
        },
      })

      if (!attempt) {
        throw new AppError('Quiz attempt not found', 404)
      }

      // Check if user owns this attempt or is instructor/admin
      const quiz = attempt.quiz
      const isOwner = attempt.userId === req.user!.id
      const isInstructor = quiz.course?.instructorId === req.user!.id || req.user!.role === 'ADMIN'

      if (!isOwner && !isInstructor) {
        throw new AppError('Access denied to this quiz attempt', 403)
      }

      // Calculate detailed results
      const detailedResults = this.getDetailedResults(quiz.questions, attempt.answers as any)

      res.json({
        success: true,
        data: {
          attempt: {
            id: attempt.id,
            score: attempt.score,
            timeSpent: attempt.timeSpent,
            startedAt: attempt.startedAt,
            completedAt: attempt.completedAt,
          },
          quiz: {
            id: quiz.id,
            title: quiz.title,
            passingScore: quiz.passingScore,
          },
          results: detailedResults,
          passed: attempt.score >= quiz.passingScore,
        },
      })
    } catch (error) {
      logger.error('Get quiz results error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Create quiz (instructor)
  async createQuiz(req: AuthenticatedRequest, res: Response) {
    try {
      const { title, description, timeLimit, passingScore, courseId, lessonId } = req.body

      // Verify instructor has access to course
      if (courseId) {
        const course = await prisma.course.findUnique({
          where: { id: courseId },
        })

        if (!course) {
          throw new AppError('Course not found', 404)
        }

        if (course.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
          throw new AppError('Access denied', 403)
        }
      }

      const quiz = await prisma.quiz.create({
        data: {
          title,
          description,
          timeLimit,
          passingScore,
          courseId,
          lessonId,
        },
      })

      res.status(201).json({
        success: true,
        message: 'Quiz created successfully',
        data: quiz,
      })
    } catch (error) {
      logger.error('Create quiz error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update quiz
  async updateQuiz(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { title, description, timeLimit, passingScore } = req.body

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const updateData: any = {}
      if (title) updateData.title = title
      if (description !== undefined) updateData.description = description
      if (timeLimit !== undefined) updateData.timeLimit = timeLimit
      if (passingScore !== undefined) updateData.passingScore = passingScore

      const updatedQuiz = await prisma.quiz.update({
        where: { id },
        data: updateData,
      })

      res.json({
        success: true,
        message: 'Quiz updated successfully',
        data: updatedQuiz,
      })
    } catch (error) {
      logger.error('Update quiz error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete quiz
  async deleteQuiz(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.quiz.delete({
        where: { id },
      })

      res.json({
        success: true,
        message: 'Quiz deleted successfully',
      })
    } catch (error) {
      logger.error('Delete quiz error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Publish quiz
  async publishQuiz(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: {
          course: true,
          _count: {
            select: { questions: true },
          },
        },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      if (quiz._count.questions === 0) {
        throw new AppError('Quiz must have at least one question before publishing', 400)
      }

      await prisma.quiz.update({
        where: { id },
        data: { isPublished: true },
      })

      res.json({
        success: true,
        message: 'Quiz published successfully',
      })
    } catch (error) {
      logger.error('Publish quiz error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Unpublish quiz
  async unpublishQuiz(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.quiz.update({
        where: { id },
        data: { isPublished: false },
      })

      res.json({
        success: true,
        message: 'Quiz unpublished successfully',
      })
    } catch (error) {
      logger.error('Unpublish quiz error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Add question to quiz
  async addQuestion(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { question, type, options, correctAnswer, explanation, points, order } = req.body

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const quizQuestion = await prisma.quizQuestion.create({
        data: {
          question,
          type,
          options,
          correctAnswer,
          explanation,
          points,
          order,
          quizId: id,
        },
      })

      res.status(201).json({
        success: true,
        message: 'Question added successfully',
        data: quizQuestion,
      })
    } catch (error) {
      logger.error('Add question error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update question
  async updateQuestion(req: AuthenticatedRequest, res: Response) {
    try {
      const { id, questionId } = req.params
      const { question, type, options, correctAnswer, explanation, points, order } = req.body

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const updateData: any = {}
      if (question) updateData.question = question
      if (type) updateData.type = type
      if (options !== undefined) updateData.options = options
      if (correctAnswer) updateData.correctAnswer = correctAnswer
      if (explanation !== undefined) updateData.explanation = explanation
      if (points !== undefined) updateData.points = points
      if (order !== undefined) updateData.order = order

      const updatedQuestion = await prisma.quizQuestion.update({
        where: { id: questionId },
        data: updateData,
      })

      res.json({
        success: true,
        message: 'Question updated successfully',
        data: updatedQuestion,
      })
    } catch (error) {
      logger.error('Update question error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete question
  async deleteQuestion(req: AuthenticatedRequest, res: Response) {
    try {
      const { id, questionId } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.quizQuestion.delete({
        where: { id: questionId },
      })

      res.json({
        success: true,
        message: 'Question deleted successfully',
      })
    } catch (error) {
      logger.error('Delete question error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get quiz analytics
  async getQuizAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const [attempts, questions] = await Promise.all([
        prisma.quizAttempt.findMany({
          where: { quizId: id },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        }),
        prisma.quizQuestion.findMany({
          where: { quizId: id },
          orderBy: { order: 'asc' },
        }),
      ])

      const analytics = this.calculateQuizAnalytics(attempts, questions, quiz.passingScore)

      res.json({
        success: true,
        data: analytics,
      })
    } catch (error) {
      logger.error('Get quiz analytics error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get all quiz attempts (instructor)
  async getAllQuizAttempts(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const quiz = await prisma.quiz.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!quiz) {
        throw new AppError('Quiz not found', 404)
      }

      if (quiz.course?.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const attempts = await prisma.quizAttempt.findMany({
        where: { quizId: id },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { startedAt: 'desc' },
      })

      res.json({
        success: true,
        data: attempts.map(attempt => ({
          ...attempt,
          passed: attempt.score >= quiz.passingScore,
        })),
      })
    } catch (error) {
      logger.error('Get all quiz attempts error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Helper methods
  async checkQuizAccess(userId: string, quiz: any): Promise<boolean> {
    if (quiz.course) {
      // Check if user is enrolled in the course
      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId,
            courseId: quiz.courseId,
          },
        },
      })

      const isInstructor = quiz.course.instructorId === userId
      return !!enrollment || isInstructor
    }

    return true // Standalone quiz
  },

  calculateQuizScore(questions: any[], answers: Record<string, string>) {
    let totalPoints = 0
    let earnedPoints = 0
    const results: any[] = []

    questions.forEach(question => {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      const isCorrect = userAnswer === question.correctAnswer

      if (isCorrect) {
        earnedPoints += question.points
      }

      results.push({
        questionId: question.id,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        points: isCorrect ? question.points : 0,
        maxPoints: question.points,
      })
    })

    const score = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0

    return { score: Math.round(score), results }
  },

  getDetailedResults(questions: any[], answers: Record<string, string>) {
    return questions.map(question => {
      const userAnswer = answers[question.id]
      const isCorrect = userAnswer === question.correctAnswer

      return {
        question: {
          id: question.id,
          text: question.question,
          type: question.type,
          options: question.options,
          points: question.points,
        },
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        explanation: question.explanation,
        pointsEarned: isCorrect ? question.points : 0,
      }
    })
  },

  async updateUserStats(userId: string, score: number) {
    const userStats = await prisma.userStats.findUnique({
      where: { userId },
    })

    if (userStats) {
      const newAverageScore = userStats.averageScore > 0
        ? (userStats.averageScore + score) / 2
        : score

      await prisma.userStats.update({
        where: { userId },
        data: {
          averageScore: newAverageScore,
          totalPoints: {
            increment: Math.round(score),
          },
        },
      })
    }
  },

  calculateQuizAnalytics(attempts: any[], questions: any[], passingScore: number) {
    const totalAttempts = attempts.length
    const completedAttempts = attempts.filter(a => a.completedAt).length
    const passedAttempts = attempts.filter(a => a.score >= passingScore).length

    const averageScore = completedAttempts > 0
      ? attempts.reduce((sum, a) => sum + a.score, 0) / completedAttempts
      : 0

    const averageTime = completedAttempts > 0
      ? attempts.reduce((sum, a) => sum + a.timeSpent, 0) / completedAttempts
      : 0

    // Question-level analytics
    const questionAnalytics = questions.map(question => {
      const questionResults = attempts
        .filter(a => a.answers && a.answers[question.id])
        .map(a => ({
          answer: a.answers[question.id],
          isCorrect: a.answers[question.id] === question.correctAnswer,
        }))

      const correctCount = questionResults.filter(r => r.isCorrect).length
      const totalResponses = questionResults.length

      return {
        questionId: question.id,
        question: question.question,
        correctCount,
        totalResponses,
        correctPercentage: totalResponses > 0 ? (correctCount / totalResponses) * 100 : 0,
        difficulty: this.calculateQuestionDifficulty(correctCount, totalResponses),
      }
    })

    return {
      overview: {
        totalAttempts,
        completedAttempts,
        passedAttempts,
        passRate: completedAttempts > 0 ? (passedAttempts / completedAttempts) * 100 : 0,
        averageScore: Math.round(averageScore),
        averageTime: Math.round(averageTime),
      },
      questionAnalytics,
      scoreDistribution: this.calculateScoreDistribution(attempts),
    }
  },

  calculateQuestionDifficulty(correctCount: number, totalResponses: number): string {
    if (totalResponses === 0) return 'unknown'
    
    const correctPercentage = (correctCount / totalResponses) * 100
    
    if (correctPercentage >= 80) return 'easy'
    if (correctPercentage >= 60) return 'medium'
    return 'hard'
  },

  calculateScoreDistribution(attempts: any[]) {
    const completedAttempts = attempts.filter(a => a.completedAt)
    const ranges = [
      { min: 0, max: 20, label: '0-20%' },
      { min: 21, max: 40, label: '21-40%' },
      { min: 41, max: 60, label: '41-60%' },
      { min: 61, max: 80, label: '61-80%' },
      { min: 81, max: 100, label: '81-100%' },
    ]

    return ranges.map(range => ({
      range: range.label,
      count: completedAttempts.filter(a => a.score >= range.min && a.score <= range.max).length,
    }))
  },
}

# Client Setup Instructions

## Current Status
The TypeScript errors have been temporarily resolved using mock implementations. The code will compile and run, but the actual HTTP requests and toast notifications will be mocked.

## Temporary Files Created
- `src/types/axios.d.ts` - Type definitions for axios
- `src/types/react-hot-toast.d.ts` - Type definitions for react-hot-toast  
- `src/types/global.d.ts` - Global type definitions
- `src/lib/mocks/axios.ts` - Mock axios implementation
- `src/lib/mocks/react-hot-toast.ts` - Mock toast implementation

## To Install Real Dependencies

### 1. Install Node.js
Download and install Node.js from [nodejs.org](https://nodejs.org/) (version 18 or higher)

### 2. Install Dependencies
```bash
# Navigate to client directory
cd client

# Install all dependencies
npm install
```

### 3. Remove Mock Files (After Installation)
Once the real packages are installed, you can:

1. Delete the mock files:
   - `src/lib/mocks/axios.ts`
   - `src/lib/mocks/react-hot-toast.ts`
   - `src/types/axios.d.ts`
   - `src/types/react-hot-toast.d.ts`

2. Update `src/lib/api/client.ts` to use real imports:
   ```typescript
   // Replace the mock imports with:
   import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
   import toast from 'react-hot-toast'
   ```

3. Update the environment variable access:
   ```typescript
   // Replace the window.ENV access with:
   baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
   ```

## Development Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
```

## Environment Variables
Create a `.env.local` file in the client directory:
```
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

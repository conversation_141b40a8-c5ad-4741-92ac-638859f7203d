import { Router } from 'express'
import { body, param } from 'express-validator'
import { lesson<PERSON>ontroller } from '@/controllers/lessonController'
import { authMiddleware, checkCourseInstructor, checkCourseEnrollment } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validateRequest'
import { uploadCourseContent } from '@/middleware/upload'

const router = Router()

// Validation rules
const createLessonValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('content')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters'),
  body('duration')
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (minutes)'),
  body('order')
    .isInt({ min: 1 })
    .withMessage('Order must be a positive integer'),
  body('courseId')
    .isUUID()
    .withMessage('Invalid course ID format'),
]

const updateLessonValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters'),
  body('duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (minutes)'),
  body('order')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order must be a positive integer'),
]

const lessonIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid lesson ID format'),
]

// All routes require authentication
router.use(authMiddleware)

// Get lessons for a course
router.get('/course/:courseId', checkCourseEnrollment, lessonController.getLessonsByCourse)

// Get single lesson
router.get('/:id', lessonIdValidation, validateRequest, lessonController.getLessonById)

// Instructor routes
router.post('/', createLessonValidation, validateRequest, lessonController.createLesson)
router.put('/:id', lessonIdValidation, updateLessonValidation, validateRequest, lessonController.updateLesson)
router.delete('/:id', lessonIdValidation, validateRequest, lessonController.deleteLesson)
router.post('/:id/publish', lessonIdValidation, validateRequest, lessonController.publishLesson)
router.post('/:id/unpublish', lessonIdValidation, validateRequest, lessonController.unpublishLesson)

// Video upload
router.post('/:id/video', lessonIdValidation, validateRequest, uploadCourseContent.single('video'), lessonController.uploadLessonVideo)

// Resources
router.post('/:id/resources', lessonIdValidation, validateRequest, uploadCourseContent.array('resources', 5), lessonController.addLessonResources)
router.delete('/:id/resources/:resourceId', lessonIdValidation, validateRequest, lessonController.deleteLessonResource)

// Progress tracking
router.post('/:id/progress', lessonIdValidation, validateRequest, lessonController.updateLessonProgress)
router.post('/:id/complete', lessonIdValidation, validateRequest, lessonController.markLessonComplete)

export default router

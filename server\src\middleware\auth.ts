import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export interface JWTPayload {
  userId: string
  iat: number
  exp: number
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('Access token is required', 401)
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    if (!token) {
      throw new AppError('Access token is required', 401)
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        preferences: true,
        stats: true,
      },
    })

    if (!user) {
      throw new AppError('User not found', 401)
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401)
    }

    // Remove password from user object
    const { password: _, ...userWithoutPassword } = user

    // Attach user to request object
    req.user = userWithoutPassword

    next()
  } catch (error) {
    logger.error('Auth middleware error:', error)
    
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid access token',
      })
    }

    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        success: false,
        message: 'Access token has expired',
      })
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      })
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}

// Role-based authorization middleware
export const authorize = (...roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      })
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
      })
    }

    next()
  }
}

// Optional auth middleware (doesn't throw error if no token)
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next()
    }

    const token = authHeader.substring(7)

    if (!token) {
      return next()
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        preferences: true,
        stats: true,
      },
    })

    if (user && user.isActive) {
      const { password: _, ...userWithoutPassword } = user
      req.user = userWithoutPassword
    }

    next()
  } catch (error) {
    // Silently continue without user if token is invalid
    next()
  }
}

// Middleware to check if user owns resource
export const checkOwnership = (resourceIdParam: string = 'id') => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
        })
      }

      const resourceId = req.params[resourceIdParam]
      const userId = req.user.id

      // For admin users, skip ownership check
      if (req.user.role === 'ADMIN') {
        return next()
      }

      // Check if user owns the resource (this is a generic check)
      // You might need to customize this based on your specific resource types
      if (resourceId !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You can only access your own resources',
        })
      }

      next()
    } catch (error) {
      logger.error('Ownership check error:', error)
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  }
}

// Middleware to check if user is instructor of a course
export const checkCourseInstructor = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      })
    }

    const courseId = req.params.courseId || req.body.courseId

    if (!courseId) {
      return res.status(400).json({
        success: false,
        message: 'Course ID is required',
      })
    }

    // Admin can access any course
    if (req.user.role === 'ADMIN') {
      return next()
    }

    // Check if user is the instructor of the course
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      select: { instructorId: true },
    })

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found',
      })
    }

    if (course.instructorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied: You are not the instructor of this course',
      })
    }

    next()
  } catch (error) {
    logger.error('Course instructor check error:', error)
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}

// Middleware to check if user is enrolled in a course
export const checkCourseEnrollment = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      })
    }

    const courseId = req.params.courseId || req.body.courseId

    if (!courseId) {
      return res.status(400).json({
        success: false,
        message: 'Course ID is required',
      })
    }

    // Admin and instructors can access any course
    if (req.user.role === 'ADMIN' || req.user.role === 'INSTRUCTOR') {
      return next()
    }

    // Check if user is enrolled in the course
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        userId_courseId: {
          userId: req.user.id,
          courseId: courseId,
        },
      },
    })

    if (!enrollment || enrollment.status !== 'ACTIVE') {
      return res.status(403).json({
        success: false,
        message: 'Access denied: You are not enrolled in this course',
      })
    }

    next()
  } catch (error) {
    logger.error('Course enrollment check error:', error)
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}

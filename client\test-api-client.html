<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Client Test</title>
</head>
<body>
    <h1>API Client Test</h1>
    <button onclick="testApiClient()">Test API Client</button>
    <div id="output"></div>

    <script>
        // Mock implementations for browser testing
        const mockToast = {
            error: (message) => {
                console.log('Toast Error:', message);
                document.getElementById('output').innerHTML += `<p style="color: red;">Toast Error: ${message}</p>`;
            },
            success: (message) => {
                console.log('Toast Success:', message);
                document.getElementById('output').innerHTML += `<p style="color: green;">Toast Success: ${message}</p>`;
            }
        };

        const mockAxios = {
            create: (config) => {
                console.log('Axios instance created with config:', config);
                return {
                    interceptors: {
                        request: { use: (fn1, fn2) => console.log('Request interceptor added') },
                        response: { use: (fn1, fn2) => console.log('Response interceptor added') }
                    },
                    get: async (url, config) => {
                        console.log('GET request to:', url);
                        return { data: { message: 'Mock GET response' } };
                    },
                    post: async (url, data, config) => {
                        console.log('POST request to:', url, 'with data:', data);
                        return { data: { message: 'Mock POST response' } };
                    }
                };
            },
            post: async (url, data) => {
                console.log('Direct axios POST to:', url);
                return { data: { token: 'mock-token' } };
            }
        };

        // Simulate your API client
        const apiClient = mockAxios.create({
            baseURL: 'http://localhost:5000/api',
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
            },
        });

        // Add interceptors
        apiClient.interceptors.request.use(
            (config) => {
                const token = localStorage.getItem('token');
                if (token && config.headers) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        apiClient.interceptors.response.use(
            (response) => {
                return response;
            },
            async (error) => {
                console.log('Response interceptor error:', error);
                mockToast.error('API Error occurred');
                return Promise.reject(error);
            }
        );

        // Test function
        async function testApiClient() {
            const output = document.getElementById('output');
            output.innerHTML = '<h3>Testing API Client...</h3>';
            
            try {
                // Test GET request
                const getResponse = await apiClient.get('/test');
                output.innerHTML += `<p>GET Response: ${JSON.stringify(getResponse.data)}</p>`;
                
                // Test POST request
                const postResponse = await apiClient.post('/test', { message: 'Hello' });
                output.innerHTML += `<p>POST Response: ${JSON.stringify(postResponse.data)}</p>`;
                
                mockToast.success('API tests completed successfully!');
            } catch (error) {
                output.innerHTML += `<p style="color: red;">Error: ${error.message}</p>`;
                mockToast.error('API test failed');
            }
        }

        console.log('API Client test page loaded. Click the button to test.');
    </script>
</body>
</html>

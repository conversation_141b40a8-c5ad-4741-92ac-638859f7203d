import { Request } from 'express'

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    email: string
    firstName: string
    lastName: string
    role: string
    avatar?: string
    bio?: string
    isActive: boolean
    isVerified: boolean
    preferences?: any
    stats?: any
  }
}

export interface JWTPayload {
  userId: string
  iat: number
  exp: number
  type?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role: 'student' | 'instructor'
  preferences?: any
}

export interface TokenResponse {
  accessToken: string
  refreshToken: string
}

import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'
import { uploadToCloudinary } from '@/utils/cloudinary'

const prisma = new PrismaClient()

export const lessonController = {
  // Get lessons for a course
  async getLessonsByCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params

      const lessons = await prisma.lesson.findMany({
        where: {
          courseId,
          isPublished: true,
        },
        include: {
          resources: true,
          quizzes: {
            where: { isPublished: true },
            select: {
              id: true,
              title: true,
              timeLimit: true,
              passingScore: true,
            },
          },
          progress: req.user ? {
            where: { userId: req.user.id },
          } : false,
        },
        orderBy: { order: 'asc' },
      })

      const lessonsWithProgress = lessons.map(lesson => ({
        ...lesson,
        userProgress: lesson.progress?.[0] || null,
        progress: undefined,
      }))

      res.json({
        success: true,
        data: lessonsWithProgress,
      })
    } catch (error) {
      logger.error('Get lessons by course error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get single lesson
  async getLessonById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructorId: true,
            },
          },
          instructor: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          resources: true,
          quizzes: {
            where: { isPublished: true },
          },
          progress: req.user ? {
            where: { userId: req.user.id },
          } : false,
        },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      // Check if user has access to this lesson
      if (req.user) {
        const enrollment = await prisma.enrollment.findUnique({
          where: {
            userId_courseId: {
              userId: req.user.id,
              courseId: lesson.courseId,
            },
          },
        })

        const isInstructor = lesson.course.instructorId === req.user.id
        const isAdmin = req.user.role === 'ADMIN'

        if (!enrollment && !isInstructor && !isAdmin) {
          throw new AppError('Access denied to this lesson', 403)
        }
      }

      const lessonData = {
        ...lesson,
        userProgress: lesson.progress?.[0] || null,
        progress: undefined,
      }

      res.json({
        success: true,
        data: lessonData,
      })
    } catch (error) {
      logger.error('Get lesson by ID error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Create new lesson
  async createLesson(req: AuthenticatedRequest, res: Response) {
    try {
      const { title, description, content, duration, order, courseId, videoUrl } = req.body

      // Check if user is instructor of the course
      const course = await prisma.course.findUnique({
        where: { id: courseId },
      })

      if (!course) {
        throw new AppError('Course not found', 404)
      }

      if (course.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const lesson = await prisma.lesson.create({
        data: {
          title,
          description,
          content,
          duration,
          order,
          courseId,
          instructorId: req.user!.id,
          videoUrl,
        },
        include: {
          instructor: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      })

      res.status(201).json({
        success: true,
        message: 'Lesson created successfully',
        data: lesson,
      })
    } catch (error) {
      logger.error('Create lesson error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update lesson
  async updateLesson(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { title, description, content, duration, order, videoUrl } = req.body

      const lesson = await prisma.lesson.findUnique({
        where: { id },
        include: { course: true },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const updateData: any = {}
      if (title) updateData.title = title
      if (description !== undefined) updateData.description = description
      if (content) updateData.content = content
      if (duration) updateData.duration = duration
      if (order) updateData.order = order
      if (videoUrl !== undefined) updateData.videoUrl = videoUrl

      const updatedLesson = await prisma.lesson.update({
        where: { id },
        data: updateData,
        include: {
          instructor: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      })

      res.json({
        success: true,
        message: 'Lesson updated successfully',
        data: updatedLesson,
      })
    } catch (error) {
      logger.error('Update lesson error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete lesson
  async deleteLesson(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.lesson.delete({
        where: { id },
      })

      res.json({
        success: true,
        message: 'Lesson deleted successfully',
      })
    } catch (error) {
      logger.error('Delete lesson error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Publish lesson
  async publishLesson(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.lesson.update({
        where: { id },
        data: { isPublished: true },
      })

      res.json({
        success: true,
        message: 'Lesson published successfully',
      })
    } catch (error) {
      logger.error('Publish lesson error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Unpublish lesson
  async unpublishLesson(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.lesson.update({
        where: { id },
        data: { isPublished: false },
      })

      res.json({
        success: true,
        message: 'Lesson unpublished successfully',
      })
    } catch (error) {
      logger.error('Unpublish lesson error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Upload lesson video
  async uploadLessonVideo(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      if (!req.file) {
        throw new AppError('No video file uploaded', 400)
      }

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const videoUrl = await uploadToCloudinary(req.file.buffer, 'lesson-videos')

      await prisma.lesson.update({
        where: { id },
        data: { videoUrl },
      })

      res.json({
        success: true,
        message: 'Video uploaded successfully',
        data: { videoUrl },
      })
    } catch (error) {
      logger.error('Upload lesson video error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Add lesson resources
  async addLessonResources(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        throw new AppError('No resource files uploaded', 400)
      }

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      const resources = await Promise.all(
        req.files.map(async (file) => {
          const url = await uploadToCloudinary(file.buffer, 'lesson-resources')
          
          return prisma.lessonResource.create({
            data: {
              lessonId: id,
              title: file.originalname,
              type: this.getFileType(file.mimetype),
              url,
              size: file.size,
            },
          })
        })
      )

      res.status(201).json({
        success: true,
        message: 'Resources added successfully',
        data: resources,
      })
    } catch (error) {
      logger.error('Add lesson resources error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete lesson resource
  async deleteLessonResource(req: AuthenticatedRequest, res: Response) {
    try {
      const { id, resourceId } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      if (lesson.instructorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new AppError('Access denied', 403)
      }

      await prisma.lessonResource.delete({
        where: { id: resourceId },
      })

      res.json({
        success: true,
        message: 'Resource deleted successfully',
      })
    } catch (error) {
      logger.error('Delete lesson resource error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update lesson progress
  async updateLessonProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { timeSpent } = req.body

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: lesson.courseId,
          },
        },
      })

      if (!enrollment) {
        throw new AppError('Not enrolled in this course', 403)
      }

      const progress = await prisma.progress.upsert({
        where: {
          userId_lessonId: {
            userId: req.user!.id,
            lessonId: id,
          },
        },
        update: {
          timeSpent: {
            increment: timeSpent,
          },
          lastAccessed: new Date(),
        },
        create: {
          userId: req.user!.id,
          lessonId: id,
          timeSpent,
          lastAccessed: new Date(),
        },
      })

      res.json({
        success: true,
        message: 'Progress updated successfully',
        data: progress,
      })
    } catch (error) {
      logger.error('Update lesson progress error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Mark lesson as complete
  async markLessonComplete(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lesson = await prisma.lesson.findUnique({
        where: { id },
      })

      if (!lesson) {
        throw new AppError('Lesson not found', 404)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: lesson.courseId,
          },
        },
      })

      if (!enrollment) {
        throw new AppError('Not enrolled in this course', 403)
      }

      const progress = await prisma.progress.upsert({
        where: {
          userId_lessonId: {
            userId: req.user!.id,
            lessonId: id,
          },
        },
        update: {
          isCompleted: true,
          completedAt: new Date(),
          lastAccessed: new Date(),
        },
        create: {
          userId: req.user!.id,
          lessonId: id,
          isCompleted: true,
          completedAt: new Date(),
          lastAccessed: new Date(),
        },
      })

      // Update course progress
      const courseProgress = await this.updateCourseProgress(req.user!.id, lesson.courseId)

      // Check if course is completed
      if (courseProgress.progressPercentage === 100) {
        await prisma.enrollment.update({
          where: {
            userId_courseId: {
              userId: req.user!.id,
              courseId: lesson.courseId,
            },
          },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        })

        // Update user stats
        await prisma.userStats.update({
          where: { userId: req.user!.id },
          data: {
            coursesCompleted: {
              increment: 1,
            },
          },
        })
      }

      res.json({
        success: true,
        message: 'Lesson marked as complete',
        data: {
          progress,
          courseProgress,
        },
      })
    } catch (error) {
      logger.error('Mark lesson complete error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Helper methods
  getFileType(mimetype: string): string {
    if (mimetype.startsWith('image/')) return 'image'
    if (mimetype.startsWith('video/')) return 'video'
    if (mimetype.startsWith('audio/')) return 'audio'
    if (mimetype === 'application/pdf') return 'pdf'
    if (mimetype.includes('document') || mimetype.includes('word')) return 'document'
    if (mimetype.includes('presentation') || mimetype.includes('powerpoint')) return 'presentation'
    return 'file'
  },

  async updateCourseProgress(userId: string, courseId: string) {
    const [totalLessons, completedLessons] = await Promise.all([
      prisma.lesson.count({
        where: {
          courseId,
          isPublished: true,
        },
      }),
      prisma.progress.count({
        where: {
          userId,
          isCompleted: true,
          lesson: {
            courseId,
            isPublished: true,
          },
        },
      }),
    ])

    const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

    await prisma.enrollment.update({
      where: {
        userId_courseId: {
          userId,
          courseId,
        },
      },
      data: {
        progress: progressPercentage,
      },
    })

    return {
      totalLessons,
      completedLessons,
      progressPercentage: Math.round(progressPercentage),
    }
  },
}

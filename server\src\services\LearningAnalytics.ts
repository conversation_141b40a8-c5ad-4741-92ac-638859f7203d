import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

interface LearningData {
  userId: string
  progress: any[]
  quizAttempts: any[]
  timeSpent: number[]
  completionRates: number[]
  engagementMetrics: any[]
}

interface PerformanceData {
  averageScore: number
  completionTime: number
  strugglingTopics: string[]
  averageTime: number
}

export class LearningAnalytics {
  // Get comprehensive learning data for a user
  async getUserLearningData(userId: string): Promise<LearningData> {
    try {
      const [progress, quizAttempts, enrollments] = await Promise.all([
        prisma.progress.findMany({
          where: { userId },
          include: {
            lesson: {
              include: {
                course: true,
              },
            },
          },
        }),
        prisma.quizAttempt.findMany({
          where: { userId },
          include: {
            quiz: {
              include: {
                course: true,
              },
            },
          },
        }),
        prisma.enrollment.findMany({
          where: { userId },
          include: {
            course: true,
          },
        }),
      ])

      const timeSpent = progress.map(p => p.timeSpent)
      const completionRates = enrollments.map(e => e.progress)
      
      return {
        userId,
        progress,
        quizAttempts,
        timeSpent,
        completionRates,
        engagementMetrics: await this.calculateEngagementMetrics(userId),
      }
    } catch (error) {
      logger.error('Error getting user learning data:', error)
      throw error
    }
  }

  // Analyze learning patterns from user data
  async analyzeLearningPatterns(learningData: LearningData) {
    try {
      const patterns = {
        studyTimePatterns: this.analyzeStudyTimePatterns(learningData),
        performancePatterns: this.analyzePerformancePatterns(learningData),
        engagementPatterns: this.analyzeEngagementPatterns(learningData),
        difficultyPreferences: this.analyzeDifficultyPreferences(learningData),
        learningVelocity: this.calculateLearningVelocity(learningData),
        retentionRate: this.calculateRetentionRate(learningData),
        preferredContentTypes: this.analyzeContentTypePreferences(learningData),
      }

      return patterns
    } catch (error) {
      logger.error('Error analyzing learning patterns:', error)
      throw error
    }
  }

  // Analyze strengths and weaknesses
  async analyzeStrengthsWeaknesses(learningData: LearningData) {
    try {
      const quizPerformance = this.analyzeQuizPerformance(learningData.quizAttempts)
      const topicPerformance = this.analyzeTopicPerformance(learningData)
      const skillAssessment = this.assessSkillLevels(learningData)

      const strengths = []
      const weaknesses = []

      // Identify strengths (topics with >80% performance)
      Object.entries(topicPerformance).forEach(([topic, performance]: [string, any]) => {
        if (performance.averageScore > 80) {
          strengths.push({
            topic,
            score: performance.averageScore,
            confidence: performance.consistency,
          })
        } else if (performance.averageScore < 60) {
          weaknesses.push({
            topic,
            score: performance.averageScore,
            issueType: this.identifyIssueType(performance),
          })
        }
      })

      const recommendedPace = this.recommendPace(learningData)
      const recommendations = this.generateImprovementRecommendations(weaknesses)

      return {
        strengths: strengths.sort((a, b) => b.score - a.score),
        weaknesses: weaknesses.sort((a, b) => a.score - b.score),
        recommendedPace,
        recommendations,
        skillAssessment,
      }
    } catch (error) {
      logger.error('Error analyzing strengths and weaknesses:', error)
      throw error
    }
  }

  // Get user performance data for specific content
  async getUserPerformanceData(userId: string, options: { courseId?: string; lessonId?: string }): Promise<PerformanceData> {
    try {
      let whereClause: any = { userId }

      if (options.courseId) {
        whereClause.quiz = { courseId: options.courseId }
      }

      if (options.lessonId) {
        whereClause.quiz = { lessonId: options.lessonId }
      }

      const quizAttempts = await prisma.quizAttempt.findMany({
        where: whereClause,
        include: {
          quiz: {
            include: {
              course: true,
              lesson: true,
            },
          },
        },
      })

      const averageScore = quizAttempts.length > 0
        ? quizAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / quizAttempts.length
        : 0

      const averageTime = quizAttempts.length > 0
        ? quizAttempts.reduce((sum, attempt) => sum + attempt.timeSpent, 0) / quizAttempts.length
        : 0

      const strugglingTopics = this.identifyStrugglingTopics(quizAttempts)

      return {
        averageScore,
        completionTime: averageTime,
        strugglingTopics,
        averageTime,
      }
    } catch (error) {
      logger.error('Error getting user performance data:', error)
      throw error
    }
  }

  // Update learning patterns based on new feedback
  async updateLearningPatterns(currentPatterns: any, feedback: any) {
    try {
      const updatedPatterns = { ...currentPatterns }

      // Update difficulty preferences
      if (!updatedPatterns.difficultyFeedback) {
        updatedPatterns.difficultyFeedback = []
      }

      updatedPatterns.difficultyFeedback.push({
        ...feedback,
        timestamp: new Date(),
      })

      // Keep only recent feedback (last 50 entries)
      if (updatedPatterns.difficultyFeedback.length > 50) {
        updatedPatterns.difficultyFeedback = updatedPatterns.difficultyFeedback.slice(-50)
      }

      // Update engagement patterns
      if (!updatedPatterns.engagementHistory) {
        updatedPatterns.engagementHistory = []
      }

      updatedPatterns.engagementHistory.push({
        contentType: feedback.contentType,
        engagement: feedback.engagement,
        timestamp: new Date(),
      })

      // Calculate updated preferences
      updatedPatterns.preferredDifficulty = this.calculatePreferredDifficulty(updatedPatterns.difficultyFeedback)
      updatedPatterns.engagementTrends = this.calculateEngagementTrends(updatedPatterns.engagementHistory)

      return updatedPatterns
    } catch (error) {
      logger.error('Error updating learning patterns:', error)
      throw error
    }
  }

  // Private helper methods
  private async calculateEngagementMetrics(userId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentProgress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: thirtyDaysAgo },
      },
    })

    const totalSessions = recentProgress.length
    const totalTime = recentProgress.reduce((sum, p) => sum + p.timeSpent, 0)
    const averageSessionTime = totalSessions > 0 ? totalTime / totalSessions : 0

    return {
      totalSessions,
      totalTime,
      averageSessionTime,
      activeDays: new Set(recentProgress.map(p => p.lastAccessed.toDateString())).size,
    }
  }

  private analyzeStudyTimePatterns(learningData: LearningData) {
    const timeSpent = learningData.timeSpent
    
    if (timeSpent.length === 0) return null

    const averageTime = timeSpent.reduce((sum, time) => sum + time, 0) / timeSpent.length
    const maxTime = Math.max(...timeSpent)
    const minTime = Math.min(...timeSpent)
    
    // Analyze time distribution
    const shortSessions = timeSpent.filter(time => time < 30).length
    const mediumSessions = timeSpent.filter(time => time >= 30 && time <= 60).length
    const longSessions = timeSpent.filter(time => time > 60).length

    return {
      averageTime,
      maxTime,
      minTime,
      sessionDistribution: {
        short: shortSessions,
        medium: mediumSessions,
        long: longSessions,
      },
      preferredSessionLength: this.determinePreferredSessionLength(timeSpent),
    }
  }

  private analyzePerformancePatterns(learningData: LearningData) {
    const quizAttempts = learningData.quizAttempts
    
    if (quizAttempts.length === 0) return null

    const scores = quizAttempts.map(attempt => attempt.score)
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
    
    // Analyze performance trends
    const recentScores = scores.slice(-10) // Last 10 attempts
    const earlierScores = scores.slice(0, -10)
    
    const recentAverage = recentScores.length > 0 
      ? recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length 
      : averageScore
    
    const earlierAverage = earlierScores.length > 0 
      ? earlierScores.reduce((sum, score) => sum + score, 0) / earlierScores.length 
      : averageScore

    const trend = recentAverage > earlierAverage ? 'improving' : 
                  recentAverage < earlierAverage ? 'declining' : 'stable'

    return {
      averageScore,
      trend,
      consistency: this.calculateConsistency(scores),
      improvementRate: recentAverage - earlierAverage,
    }
  }

  private analyzeEngagementPatterns(learningData: LearningData) {
    const engagementMetrics = learningData.engagementMetrics
    
    return {
      averageSessionTime: engagementMetrics.averageSessionTime,
      sessionFrequency: engagementMetrics.totalSessions / 30, // per day
      activeDaysRatio: engagementMetrics.activeDays / 30,
      engagementLevel: this.calculateEngagementLevel(engagementMetrics),
    }
  }

  private analyzeDifficultyPreferences(learningData: LearningData) {
    // Analyze performance across different difficulty levels
    const performanceByDifficulty = {}
    
    // This would require difficulty metadata on content
    // For now, return a placeholder
    return {
      preferredDifficulty: 'medium',
      adaptabilityScore: 0.7,
    }
  }

  private calculateLearningVelocity(learningData: LearningData) {
    const completedLessons = learningData.progress.filter(p => p.isCompleted)
    
    if (completedLessons.length < 2) return 0

    // Calculate lessons completed per week
    const firstCompletion = new Date(Math.min(...completedLessons.map(p => p.completedAt?.getTime() || 0)))
    const lastCompletion = new Date(Math.max(...completedLessons.map(p => p.completedAt?.getTime() || 0)))
    
    const weeksDiff = (lastCompletion.getTime() - firstCompletion.getTime()) / (1000 * 60 * 60 * 24 * 7)
    
    return weeksDiff > 0 ? completedLessons.length / weeksDiff : 0
  }

  private calculateRetentionRate(learningData: LearningData) {
    // Calculate how well user retains information over time
    // This would require follow-up assessments
    return 0.8 // Placeholder
  }

  private analyzeContentTypePreferences(learningData: LearningData) {
    // Analyze performance and engagement across different content types
    return {
      video: 0.8,
      text: 0.6,
      interactive: 0.9,
      quiz: 0.7,
    }
  }

  private analyzeQuizPerformance(quizAttempts: any[]) {
    if (quizAttempts.length === 0) return {}

    const performanceByQuiz = {}
    
    quizAttempts.forEach(attempt => {
      const quizId = attempt.quizId
      if (!performanceByQuiz[quizId]) {
        performanceByQuiz[quizId] = []
      }
      performanceByQuiz[quizId].push(attempt.score)
    })

    return Object.entries(performanceByQuiz).reduce((acc, [quizId, scores]: [string, any]) => {
      acc[quizId] = {
        averageScore: scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length,
        attempts: scores.length,
        bestScore: Math.max(...scores),
        consistency: this.calculateConsistency(scores),
      }
      return acc
    }, {} as any)
  }

  private analyzeTopicPerformance(learningData: LearningData) {
    // Group performance by topic/category
    const topicPerformance = {}
    
    learningData.quizAttempts.forEach(attempt => {
      const topic = attempt.quiz.course?.category || 'general'
      if (!topicPerformance[topic]) {
        topicPerformance[topic] = []
      }
      topicPerformance[topic].push(attempt.score)
    })

    return Object.entries(topicPerformance).reduce((acc, [topic, scores]: [string, any]) => {
      acc[topic] = {
        averageScore: scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length,
        attempts: scores.length,
        consistency: this.calculateConsistency(scores),
      }
      return acc
    }, {} as any)
  }

  private assessSkillLevels(learningData: LearningData) {
    // Assess skill levels across different areas
    return {
      beginner: 0.3,
      intermediate: 0.5,
      advanced: 0.2,
    }
  }

  private identifyIssueType(performance: any) {
    if (performance.consistency < 0.5) {
      return 'inconsistent_performance'
    } else if (performance.averageScore < 40) {
      return 'fundamental_gaps'
    } else {
      return 'needs_practice'
    }
  }

  private recommendPace(learningData: LearningData) {
    const velocity = this.calculateLearningVelocity(learningData)
    const performance = this.analyzePerformancePatterns(learningData)
    
    if (performance && performance.averageScore > 85 && velocity > 2) {
      return 'FAST'
    } else if (performance && performance.averageScore < 60) {
      return 'SLOW'
    } else {
      return 'NORMAL'
    }
  }

  private generateImprovementRecommendations(weaknesses: any[]) {
    return weaknesses.map(weakness => {
      switch (weakness.issueType) {
        case 'fundamental_gaps':
          return `Review fundamentals in ${weakness.topic}`
        case 'inconsistent_performance':
          return `Practice more consistently in ${weakness.topic}`
        case 'needs_practice':
          return `Increase practice time for ${weakness.topic}`
        default:
          return `Focus on improving ${weakness.topic}`
      }
    })
  }

  private identifyStrugglingTopics(quizAttempts: any[]) {
    const topicScores = {}
    
    quizAttempts.forEach(attempt => {
      const topic = attempt.quiz.course?.category || 'general'
      if (!topicScores[topic]) {
        topicScores[topic] = []
      }
      topicScores[topic].push(attempt.score)
    })

    return Object.entries(topicScores)
      .filter(([_, scores]: [string, any]) => {
        const avgScore = scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length
        return avgScore < 60
      })
      .map(([topic, _]) => topic)
  }

  private determinePreferredSessionLength(timeSpent: number[]) {
    const sessionCounts = {
      short: timeSpent.filter(time => time < 30).length,
      medium: timeSpent.filter(time => time >= 30 && time <= 60).length,
      long: timeSpent.filter(time => time > 60).length,
    }

    return Object.entries(sessionCounts).reduce((a, b) => 
      sessionCounts[a[0] as keyof typeof sessionCounts] > sessionCounts[b[0] as keyof typeof sessionCounts] ? a : b
    )[0]
  }

  private calculateConsistency(scores: number[]) {
    if (scores.length < 2) return 1

    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
    const standardDeviation = Math.sqrt(variance)
    
    // Normalize consistency score (lower standard deviation = higher consistency)
    return Math.max(0, 1 - (standardDeviation / 100))
  }

  private calculateEngagementLevel(metrics: any) {
    const sessionScore = Math.min(metrics.totalSessions / 20, 1) // Max 20 sessions
    const timeScore = Math.min(metrics.totalTime / 1800, 1) // Max 30 hours
    const frequencyScore = Math.min(metrics.activeDays / 20, 1) // Max 20 active days
    
    return (sessionScore + timeScore + frequencyScore) / 3
  }

  private calculatePreferredDifficulty(feedbackHistory: any[]) {
    if (feedbackHistory.length === 0) return 'medium'

    const recentFeedback = feedbackHistory.slice(-10)
    const avgDifficulty = recentFeedback.reduce((sum, feedback) => sum + feedback.difficulty, 0) / recentFeedback.length

    if (avgDifficulty > 4) return 'hard'
    if (avgDifficulty < 2) return 'easy'
    return 'medium'
  }

  private calculateEngagementTrends(engagementHistory: any[]) {
    if (engagementHistory.length < 5) return 'stable'

    const recent = engagementHistory.slice(-5)
    const earlier = engagementHistory.slice(-10, -5)

    if (earlier.length === 0) return 'stable'

    const recentAvg = recent.reduce((sum, item) => sum + item.engagement, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, item) => sum + item.engagement, 0) / earlier.length

    if (recentAvg > earlierAvg + 0.5) return 'increasing'
    if (recentAvg < earlierAvg - 0.5) return 'decreasing'
    return 'stable'
  }
}

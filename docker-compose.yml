version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: elearning_postgres
    environment:
      POSTGRES_DB: elearning_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - elearning_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: elearning_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - elearning_network

  # Backend Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: elearning_server
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: ***********************************************/elearning_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
      JWT_EXPIRES_IN: 15m
      JWT_REFRESH_EXPIRES_IN: 7d
      CLIENT_URL: http://localhost:3000
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: your-app-password
      EMAIL_FROM: <EMAIL>
      MAX_FILE_SIZE: 10485760
      CLOUDINARY_CLOUD_NAME: your-cloud-name
      CLOUDINARY_API_KEY: your-api-key
      CLOUDINARY_API_SECRET: your-api-secret
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./server:/app
      - /app/node_modules
    networks:
      - elearning_network
    command: npm run dev

  # Frontend Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: elearning_client
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5000/api
      NEXT_PUBLIC_SOCKET_URL: http://localhost:5000
    ports:
      - "3000:3000"
    depends_on:
      - server
    volumes:
      - ./client:/app
      - /app/node_modules
      - /app/.next
    networks:
      - elearning_network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  elearning_network:
    driver: bridge

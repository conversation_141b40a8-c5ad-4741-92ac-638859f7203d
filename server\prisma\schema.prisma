// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      Role     @default(STUDENT)
  avatar    String?
  bio       String?
  isActive  Boolean  @default(true)
  isVerified Boolean @default(false)
  lastLoginAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  preferences UserPreferences?
  stats       UserStats?
  enrollments Enrollment[]
  progress    Progress[]
  quizAttempts QuizAttempt[]
  assignments AssignmentSubmission[]
  discussions DiscussionPost[]
  notifications Notification[]
  badges      UserBadge[]
  
  // Instructor relationships
  coursesCreated Course[] @relation("CourseInstructor")
  lessonsCreated Lesson[] @relation("LessonInstructor")
  
  // Adaptive learning
  learningPath LearningPath[]
  adaptiveData AdaptiveData?

  @@map("users")
}

model UserPreferences {
  id           String       @id @default(cuid())
  userId       String       @unique
  learningStyle LearningStyle @default(VISUAL)
  difficulty   Difficulty   @default(BEGINNER)
  pace         Pace         @default(NORMAL)
  notifications Json         @default("{\"email\": true, \"push\": true, \"reminders\": true}")
  theme        Theme        @default(LIGHT)
  language     String       @default("en")
  timezone     String       @default("UTC")
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model UserStats {
  id                String   @id @default(cuid())
  userId            String   @unique
  coursesEnrolled   Int      @default(0)
  coursesCompleted  Int      @default(0)
  totalLearningTime Int      @default(0) // in minutes
  streakDays        Int      @default(0)
  averageScore      Float    @default(0)
  totalPoints       Int      @default(0)
  level             Int      @default(1)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_stats")
}

model Course {
  id          String      @id @default(cuid())
  title       String
  description String
  thumbnail   String?
  category    String
  level       Difficulty  @default(BEGINNER)
  duration    Int         // in minutes
  price       Float       @default(0)
  isPublished Boolean     @default(false)
  instructorId String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  instructor  User         @relation("CourseInstructor", fields: [instructorId], references: [id])
  lessons     Lesson[]
  enrollments Enrollment[]
  quizzes     Quiz[]
  assignments Assignment[]
  tags        CourseTag[]
  reviews     CourseReview[]

  @@map("courses")
}

model Lesson {
  id           String      @id @default(cuid())
  title        String
  description  String?
  content      String      // Rich text content
  videoUrl     String?
  duration     Int         // in minutes
  order        Int
  isPublished  Boolean     @default(false)
  courseId     String
  instructorId String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relationships
  course      Course     @relation(fields: [courseId], references: [id], onDelete: Cascade)
  instructor  User       @relation("LessonInstructor", fields: [instructorId], references: [id])
  progress    Progress[]
  quizzes     Quiz[]
  resources   LessonResource[]

  @@map("lessons")
}

model Quiz {
  id          String     @id @default(cuid())
  title       String
  description String?
  timeLimit   Int?       // in minutes
  passingScore Float     @default(70)
  courseId    String?
  lessonId    String?
  isPublished Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relationships
  course    Course?       @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lesson    Lesson?       @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  questions QuizQuestion[]
  attempts  QuizAttempt[]

  @@map("quizzes")
}

model QuizQuestion {
  id            String       @id @default(cuid())
  question      String
  type          QuestionType @default(MULTIPLE_CHOICE)
  options       Json?        // Array of options for multiple choice
  correctAnswer String
  explanation   String?
  points        Int          @default(1)
  order         Int
  quizId        String
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_questions")
}

model Enrollment {
  id           String           @id @default(cuid())
  userId       String
  courseId     String
  status       EnrollmentStatus @default(ACTIVE)
  progress     Float            @default(0) // percentage
  completedAt  DateTime?
  enrolledAt   DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relationships
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("enrollments")
}

model Progress {
  id          String    @id @default(cuid())
  userId      String
  lessonId    String
  isCompleted Boolean   @default(false)
  timeSpent   Int       @default(0) // in minutes
  lastAccessed DateTime @default(now())
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([userId, lessonId])
  @@map("progress")
}

// Enums
enum Role {
  STUDENT
  INSTRUCTOR
  ADMIN
}

enum LearningStyle {
  VISUAL
  AUDITORY
  KINESTHETIC
  READING
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum Pace {
  SLOW
  NORMAL
  FAST
}

enum Theme {
  LIGHT
  DARK
  SYSTEM
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
  ESSAY
  FILL_BLANK
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

// Additional models for comprehensive e-learning platform
model QuizAttempt {
  id        String   @id @default(cuid())
  userId    String
  quizId    String
  score     Float
  answers   Json     // User's answers
  timeSpent Int      // in minutes
  startedAt DateTime @default(now())
  completedAt DateTime?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
}

model Assignment {
  id          String    @id @default(cuid())
  title       String
  description String
  dueDate     DateTime?
  maxPoints   Int       @default(100)
  courseId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  course      Course                 @relation(fields: [courseId], references: [id], onDelete: Cascade)
  submissions AssignmentSubmission[]

  @@map("assignments")
}

model AssignmentSubmission {
  id           String    @id @default(cuid())
  userId       String
  assignmentId String
  content      String
  attachments  Json?     // Array of file URLs
  score        Float?
  feedback     String?
  submittedAt  DateTime  @default(now())
  gradedAt     DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignment Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)

  @@unique([userId, assignmentId])
  @@map("assignment_submissions")
}

model DiscussionPost {
  id        String   @id @default(cuid())
  title     String?
  content   String
  userId    String
  courseId  String?
  parentId  String?  // For replies
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user     User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent   DiscussionPost?   @relation("PostReplies", fields: [parentId], references: [id])
  replies  DiscussionPost[]  @relation("PostReplies")

  @@map("discussion_posts")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String   // 'course', 'assignment', 'quiz', 'system'
  isRead    Boolean  @default(false)
  data      Json?    // Additional data
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Badge {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  icon        String
  criteria    Json     // Criteria for earning the badge
  createdAt   DateTime @default(now())

  userBadges UserBadge[]

  @@map("badges")
}

model UserBadge {
  id       String   @id @default(cuid())
  userId   String
  badgeId  String
  earnedAt DateTime @default(now())

  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  badge Badge @relation(fields: [badgeId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeId])
  @@map("user_badges")
}

model CourseTag {
  id       String @id @default(cuid())
  courseId String
  tag      String

  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([courseId, tag])
  @@map("course_tags")
}

model CourseReview {
  id        String   @id @default(cuid())
  userId    String
  courseId  String
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("course_reviews")
}

model LessonResource {
  id       String @id @default(cuid())
  lessonId String
  title    String
  type     String // 'pdf', 'video', 'link', 'document'
  url      String
  size     Int?   // file size in bytes

  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@map("lesson_resources")
}

// Adaptive Learning Models
model LearningPath {
  id        String   @id @default(cuid())
  userId    String
  courseId  String?
  path      Json     // Ordered array of lesson/content IDs
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("learning_paths")
}

model AdaptiveData {
  id                String   @id @default(cuid())
  userId            String   @unique
  learningPatterns  Json     // ML-analyzed learning patterns
  strengths         Json     // Subject/skill strengths
  weaknesses        Json     // Areas needing improvement
  recommendedPace   Pace     @default(NORMAL)
  nextRecommendations Json   // Next recommended content
  lastAnalyzed      DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("adaptive_data")
}

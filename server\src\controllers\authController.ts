import { Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { generateTokens, verifyRefreshToken } from '@/utils/jwt'
import { sendEmail } from '@/utils/email'
import { logger } from '@/utils/logger'
import { AppError } from '@/utils/AppError'
import { generateResetToken, verifyResetToken } from '@/utils/crypto'
import { uploadToCloudinary } from '@/utils/cloudinary'

const prisma = new PrismaClient()

export const authController = {
  // Register new user
  async register(req: Request, res: Response) {
    try {
      const { email, password, firstName, lastName, role, preferences } = req.body

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      })

      if (existingUser) {
        throw new AppError('User with this email already exists', 400)
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Create user with preferences and stats
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          role: role.toUpperCase(),
          preferences: {
            create: {
              learningStyle: preferences?.learningStyle || 'VISUAL',
              difficulty: preferences?.difficulty || 'BEGINNER',
              pace: preferences?.pace || 'NORMAL',
              notifications: preferences?.notifications || {
                email: true,
                push: true,
                reminders: true,
              },
              theme: preferences?.theme || 'LIGHT',
              language: preferences?.language || 'en',
            },
          },
          stats: {
            create: {},
          },
        },
        include: {
          preferences: true,
          stats: true,
        },
      })

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user.id)

      // Send welcome email
      try {
        await sendEmail({
          to: user.email,
          subject: 'Welcome to Adaptive E-Learning Platform',
          template: 'welcome',
          data: {
            firstName: user.firstName,
            verificationUrl: `${process.env.CLIENT_URL}/auth/verify-email?token=${accessToken}`,
          },
        })
      } catch (emailError) {
        logger.error('Failed to send welcome email:', emailError)
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: userWithoutPassword,
          token: accessToken,
          refreshToken,
        },
      })
    } catch (error) {
      logger.error('Registration error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Login user
  async login(req: Request, res: Response) {
    try {
      const { email, password, rememberMe } = req.body

      // Find user with preferences and stats
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          preferences: true,
          stats: true,
        },
      })

      if (!user) {
        throw new AppError('Invalid email or password', 401)
      }

      if (!user.isActive) {
        throw new AppError('Account is deactivated. Please contact support.', 401)
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password)
      if (!isPasswordValid) {
        throw new AppError('Invalid email or password', 401)
      }

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      })

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user.id, rememberMe)

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: userWithoutPassword,
          token: accessToken,
          refreshToken,
        },
      })
    } catch (error) {
      logger.error('Login error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Logout user
  async logout(req: Request, res: Response) {
    try {
      // In a production app, you might want to blacklist the token
      res.json({
        success: true,
        message: 'Logout successful',
      })
    } catch (error) {
      logger.error('Logout error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Refresh token
  async refreshToken(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body

      if (!refreshToken) {
        throw new AppError('Refresh token is required', 401)
      }

      const userId = verifyRefreshToken(refreshToken)
      const { accessToken, refreshToken: newRefreshToken } = generateTokens(userId)

      res.json({
        success: true,
        data: {
          token: accessToken,
          refreshToken: newRefreshToken,
        },
      })
    } catch (error) {
      logger.error('Refresh token error:', error)
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
      })
    }
  },

  // Get user profile
  async getProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user!.id },
        include: {
          preferences: true,
          stats: true,
          badges: {
            include: {
              badge: true,
            },
          },
        },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      res.json({
        success: true,
        data: userWithoutPassword,
      })
    } catch (error) {
      logger.error('Get profile error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update user profile
  async updateProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const { firstName, lastName, bio, preferences } = req.body

      const updateData: any = {}
      if (firstName) updateData.firstName = firstName
      if (lastName) updateData.lastName = lastName
      if (bio !== undefined) updateData.bio = bio

      const user = await prisma.user.update({
        where: { id: req.user!.id },
        data: updateData,
        include: {
          preferences: true,
          stats: true,
        },
      })

      // Update preferences if provided
      if (preferences) {
        await prisma.userPreferences.update({
          where: { userId: req.user!.id },
          data: preferences,
        })
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: userWithoutPassword,
      })
    } catch (error) {
      logger.error('Update profile error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Upload avatar
  async uploadAvatar(req: AuthenticatedRequest, res: Response) {
    try {
      if (!req.file) {
        throw new AppError('No file uploaded', 400)
      }

      // Upload to cloudinary or local storage
      const avatarUrl = await uploadToCloudinary(req.file.buffer, 'avatars')

      // Update user avatar
      await prisma.user.update({
        where: { id: req.user!.id },
        data: { avatar: avatarUrl },
      })

      res.json({
        success: true,
        message: 'Avatar uploaded successfully',
        data: { avatarUrl },
      })
    } catch (error) {
      logger.error('Upload avatar error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Request password reset
  async requestPasswordReset(req: Request, res: Response) {
    try {
      const { email } = req.body

      const user = await prisma.user.findUnique({
        where: { email },
      })

      if (!user) {
        // Don't reveal if email exists
        res.json({
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent.',
        })
        return
      }

      const resetToken = generateResetToken()
      
      // Store reset token (in production, use Redis with expiration)
      // For now, we'll use a simple approach
      
      await sendEmail({
        to: user.email,
        subject: 'Password Reset Request',
        template: 'password-reset',
        data: {
          firstName: user.firstName,
          resetUrl: `${process.env.CLIENT_URL}/auth/reset-password?token=${resetToken}`,
        },
      })

      res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      })
    } catch (error) {
      logger.error('Password reset request error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Reset password
  async resetPassword(req: Request, res: Response) {
    try {
      const { token, password } = req.body

      // Verify reset token (implement proper token verification)
      const isValidToken = verifyResetToken(token)
      if (!isValidToken) {
        throw new AppError('Invalid or expired reset token', 400)
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Update password (you'll need to implement token-to-user mapping)
      // This is a simplified version
      res.json({
        success: true,
        message: 'Password reset successfully',
      })
    } catch (error) {
      logger.error('Password reset error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Change password
  async changePassword(req: AuthenticatedRequest, res: Response) {
    try {
      const { currentPassword, newPassword } = req.body

      const user = await prisma.user.findUnique({
        where: { id: req.user!.id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
      if (!isCurrentPasswordValid) {
        throw new AppError('Current password is incorrect', 400)
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12)

      // Update password
      await prisma.user.update({
        where: { id: req.user!.id },
        data: { password: hashedNewPassword },
      })

      res.json({
        success: true,
        message: 'Password changed successfully',
      })
    } catch (error) {
      logger.error('Change password error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Send verification email
  async sendVerificationEmail(req: AuthenticatedRequest, res: Response) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user!.id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      if (user.isVerified) {
        throw new AppError('Email is already verified', 400)
      }

      const verificationToken = jwt.sign(
        { userId: user.id, type: 'email_verification' },
        process.env.JWT_SECRET!,
        { expiresIn: '24h' }
      )

      await sendEmail({
        to: user.email,
        subject: 'Verify Your Email Address',
        template: 'email-verification',
        data: {
          firstName: user.firstName,
          verificationUrl: `${process.env.CLIENT_URL}/auth/verify-email?token=${verificationToken}`,
        },
      })

      res.json({
        success: true,
        message: 'Verification email sent successfully',
      })
    } catch (error) {
      logger.error('Send verification email error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Verify email
  async verifyEmail(req: Request, res: Response) {
    try {
      const { token } = req.body

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
      
      if (decoded.type !== 'email_verification') {
        throw new AppError('Invalid verification token', 400)
      }

      await prisma.user.update({
        where: { id: decoded.userId },
        data: { isVerified: true },
      })

      res.json({
        success: true,
        message: 'Email verified successfully',
      })
    } catch (error) {
      logger.error('Email verification error:', error)
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
      })
    }
  },

  // Deactivate account
  async deactivateAccount(req: AuthenticatedRequest, res: Response) {
    try {
      await prisma.user.update({
        where: { id: req.user!.id },
        data: { isActive: false },
      })

      res.json({
        success: true,
        message: 'Account deactivated successfully',
      })
    } catch (error) {
      logger.error('Deactivate account error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Reactivate account
  async reactivateAccount(req: Request, res: Response) {
    try {
      const { email } = req.body

      const user = await prisma.user.findUnique({
        where: { email },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      await prisma.user.update({
        where: { id: user.id },
        data: { isActive: true },
      })

      res.json({
        success: true,
        message: 'Account reactivated successfully',
      })
    } catch (error) {
      logger.error('Reactivate account error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete account
  async deleteAccount(req: AuthenticatedRequest, res: Response) {
    try {
      await prisma.user.delete({
        where: { id: req.user!.id },
      })

      res.json({
        success: true,
        message: 'Account deleted successfully',
      })
    } catch (error) {
      logger.error('Delete account error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },
}

import Redis from 'ioredis'
import { logger } from '@/utils/logger'

let redis: Redis | null = null

export const connectRedis = async (): Promise<Redis> => {
  try {
    redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    })

    redis.on('connect', () => {
      logger.info('Redis connected successfully')
    })

    redis.on('error', (error) => {
      logger.error('Redis connection error:', error)
    })

    redis.on('ready', () => {
      logger.info('Redis is ready')
    })

    redis.on('close', () => {
      logger.info('Redis connection closed')
    })

    return redis
  } catch (error) {
    logger.error('Failed to connect to Redis:', error)
    throw error
  }
}

export const getRedisClient = (): Redis => {
  if (!redis) {
    throw new Error('Redis client not initialized')
  }
  return redis
}

export const disconnectRedis = async (): Promise<void> => {
  if (redis) {
    await redis.quit()
    redis = null
    logger.info('Redis disconnected successfully')
  }
}

// Cache utilities
export const cacheService = {
  async get(key: string): Promise<string | null> {
    const client = getRedisClient()
    return await client.get(key)
  },

  async set(key: string, value: string, ttl?: number): Promise<void> {
    const client = getRedisClient()
    if (ttl) {
      await client.setex(key, ttl, value)
    } else {
      await client.set(key, value)
    }
  },

  async del(key: string): Promise<void> {
    const client = getRedisClient()
    await client.del(key)
  },

  async exists(key: string): Promise<boolean> {
    const client = getRedisClient()
    const result = await client.exists(key)
    return result === 1
  },

  async setJson(key: string, value: any, ttl?: number): Promise<void> {
    await this.set(key, JSON.stringify(value), ttl)
  },

  async getJson<T>(key: string): Promise<T | null> {
    const value = await this.get(key)
    return value ? JSON.parse(value) : null
  },

  async increment(key: string, ttl?: number): Promise<number> {
    const client = getRedisClient()
    const result = await client.incr(key)
    if (ttl && result === 1) {
      await client.expire(key, ttl)
    }
    return result
  },

  async setHash(key: string, field: string, value: string): Promise<void> {
    const client = getRedisClient()
    await client.hset(key, field, value)
  },

  async getHash(key: string, field: string): Promise<string | null> {
    const client = getRedisClient()
    return await client.hget(key, field)
  },

  async getAllHash(key: string): Promise<Record<string, string>> {
    const client = getRedisClient()
    return await client.hgetall(key)
  },
}

// Session management
export const sessionService = {
  async createSession(userId: string, sessionData: any, ttl: number = 86400): Promise<string> {
    const sessionId = `session:${userId}:${Date.now()}`
    await cacheService.setJson(sessionId, sessionData, ttl)
    return sessionId
  },

  async getSession(sessionId: string): Promise<any> {
    return await cacheService.getJson(sessionId)
  },

  async updateSession(sessionId: string, sessionData: any, ttl: number = 86400): Promise<void> {
    await cacheService.setJson(sessionId, sessionData, ttl)
  },

  async deleteSession(sessionId: string): Promise<void> {
    await cacheService.del(sessionId)
  },
}

// Rate limiting
export const rateLimitService = {
  async checkRateLimit(key: string, limit: number, window: number): Promise<{ allowed: boolean; remaining: number }> {
    const current = await cacheService.increment(key, window)
    const remaining = Math.max(0, limit - current)
    return {
      allowed: current <= limit,
      remaining,
    }
  },
}

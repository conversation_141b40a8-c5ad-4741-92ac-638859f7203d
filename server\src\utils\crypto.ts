import crypto from 'crypto'

export const generateResetToken = (): string => {
  return crypto.randomBytes(32).toString('hex')
}

export const verifyResetToken = (token: string): boolean => {
  // In a real implementation, you would check this against stored tokens in Redis
  // For now, we'll just validate the format
  return /^[a-f0-9]{64}$/.test(token)
}

export const generateVerificationCode = (): string => {
  return crypto.randomInt(100000, 999999).toString()
}

export const hashPassword = async (password: string): Promise<string> => {
  const bcrypt = await import('bcryptjs')
  return bcrypt.hash(password, 12)
}

export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  const bcrypt = await import('bcryptjs')
  return bcrypt.compare(password, hash)
}

export const generateSecureId = (): string => {
  return crypto.randomUUID()
}

export const generateApiKey = (): string => {
  return crypto.randomBytes(32).toString('base64url')
}

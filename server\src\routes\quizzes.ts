import { Router } from 'express'
import { body, param } from 'express-validator'
import { quizController } from '@/controllers/quizController'
import { authMiddleware, checkCourseInstructor, checkCourseEnrollment } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validateRequest'

const router = Router()

// Validation rules
const createQuizValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('timeLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Time limit must be a positive integer (minutes)'),
  body('passingScore')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Passing score must be between 0 and 100'),
  body('courseId')
    .optional()
    .isUUID()
    .withMessage('Invalid course ID format'),
  body('lessonId')
    .optional()
    .isUUID()
    .withMessage('Invalid lesson ID format'),
]

const createQuestionValidation = [
  body('question')
    .trim()
    .isLength({ min: 5 })
    .withMessage('Question must be at least 5 characters'),
  body('type')
    .isIn(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER', 'ESSAY', 'FILL_BLANK'])
    .withMessage('Invalid question type'),
  body('options')
    .optional()
    .isArray()
    .withMessage('Options must be an array'),
  body('correctAnswer')
    .trim()
    .notEmpty()
    .withMessage('Correct answer is required'),
  body('explanation')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Explanation must not exceed 500 characters'),
  body('points')
    .isInt({ min: 1 })
    .withMessage('Points must be a positive integer'),
  body('order')
    .isInt({ min: 1 })
    .withMessage('Order must be a positive integer'),
]

const submitAnswerValidation = [
  body('answers')
    .isArray()
    .withMessage('Answers must be an array'),
  body('answers.*.questionId')
    .isUUID()
    .withMessage('Invalid question ID format'),
  body('answers.*.answer')
    .notEmpty()
    .withMessage('Answer is required'),
]

const quizIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid quiz ID format'),
]

// All routes require authentication
router.use(authMiddleware)

// Student routes
router.get('/:id', quizIdValidation, validateRequest, quizController.getQuizById)
router.post('/:id/start', quizIdValidation, validateRequest, quizController.startQuizAttempt)
router.post('/:id/submit', quizIdValidation, submitAnswerValidation, validateRequest, quizController.submitQuizAttempt)
router.get('/:id/attempts', quizIdValidation, validateRequest, quizController.getMyQuizAttempts)
router.get('/:id/results/:attemptId', validateRequest, quizController.getQuizResults)

// Instructor routes
router.post('/', createQuizValidation, validateRequest, quizController.createQuiz)
router.put('/:id', quizIdValidation, validateRequest, quizController.updateQuiz)
router.delete('/:id', quizIdValidation, validateRequest, quizController.deleteQuiz)
router.post('/:id/publish', quizIdValidation, validateRequest, quizController.publishQuiz)
router.post('/:id/unpublish', quizIdValidation, validateRequest, quizController.unpublishQuiz)

// Question management
router.post('/:id/questions', quizIdValidation, createQuestionValidation, validateRequest, quizController.addQuestion)
router.put('/:id/questions/:questionId', quizIdValidation, validateRequest, quizController.updateQuestion)
router.delete('/:id/questions/:questionId', quizIdValidation, validateRequest, quizController.deleteQuestion)

// Quiz analytics
router.get('/:id/analytics', quizIdValidation, validateRequest, quizController.getQuizAnalytics)
router.get('/:id/attempts/all', quizIdValidation, validateRequest, quizController.getAllQuizAttempts)

export default router

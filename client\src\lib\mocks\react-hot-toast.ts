// Mock implementation of react-hot-toast for development until the real package is installed

interface ToastOptions {
  id?: string;
  duration?: number;
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  style?: React.CSSProperties;
  className?: string;
  icon?: React.ReactNode;
}

const mockToast = (message: string, options?: ToastOptions): string => {
  console.log('Mock Toast:', message, options)
  return Math.random().toString(36).substr(2, 9)
}

mockToast.success = (message: string, options?: ToastOptions): string => {
  console.log('Mock Success Toast:', message, options)
  return Math.random().toString(36).substr(2, 9)
}

mockToast.error = (message: string, options?: ToastOptions): string => {
  console.log('Mock Error Toast:', message, options)
  return Math.random().toString(36).substr(2, 9)
}

mockToast.loading = (message: string, options?: ToastOptions): string => {
  console.log('Mock Loading Toast:', message, options)
  return Math.random().toString(36).substr(2, 9)
}

mockToast.custom = (jsx: React.ReactNode, options?: ToastOptions): string => {
  console.log('Mock Custom Toast:', jsx, options)
  return Math.random().toString(36).substr(2, 9)
}

mockToast.dismiss = (toastId?: string): void => {
  console.log('Mock Dismiss Toast:', toastId)
}

mockToast.remove = (toastId?: string): void => {
  console.log('Mock Remove Toast:', toastId)
}

mockToast.promise = async <T>(
  promise: Promise<T>,
  msgs: {
    loading: string;
    success: string | ((data: T) => string);
    error: string | ((err: any) => string);
  },
  options?: ToastOptions
): Promise<T> => {
  console.log('Mock Promise Toast:', msgs, options)
  return promise
}

export default mockToast

import { Router } from 'express'
import { body } from 'express-validator'
import { authController } from '@/controllers/authController'
import { authMiddleware } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validateRequest'
import { upload } from '@/middleware/upload'

const router = Router()

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('role')
    .isIn(['student', 'instructor'])
    .withMessage('Role must be either student or instructor'),
]

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
]

const passwordResetRequestValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
]

const passwordResetValidation = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match')
      }
      return true
    }),
]

const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
]

const updateProfileValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Bio must not exceed 500 characters'),
]

// Authentication routes
router.post('/register', registerValidation, validateRequest, authController.register)
router.post('/login', loginValidation, validateRequest, authController.login)
router.post('/logout', authController.logout)
router.post('/refresh', authController.refreshToken)

// Profile routes
router.get('/profile', authMiddleware, authController.getProfile)
router.put('/profile', authMiddleware, updateProfileValidation, validateRequest, authController.updateProfile)
router.post('/avatar', authMiddleware, upload.single('avatar'), authController.uploadAvatar)

// Password management
router.post('/password-reset-request', passwordResetRequestValidation, validateRequest, authController.requestPasswordReset)
router.post('/password-reset', passwordResetValidation, validateRequest, authController.resetPassword)
router.post('/change-password', authMiddleware, changePasswordValidation, validateRequest, authController.changePassword)

// Email verification
router.post('/send-verification', authMiddleware, authController.sendVerificationEmail)
router.post('/verify-email', authController.verifyEmail)

// Account management
router.post('/deactivate', authMiddleware, authController.deactivateAccount)
router.post('/reactivate', authController.reactivateAccount)
router.delete('/account', authMiddleware, authController.deleteAccount)

export default router

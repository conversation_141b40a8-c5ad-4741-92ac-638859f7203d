import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import StudentDashboard from '@/components/dashboard/StudentDashboard'

// Mock fetch
global.fetch = jest.fn()

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock recharts components
jest.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
}))

const mockDashboardData = {
  stats: {
    coursesEnrolled: 5,
    coursesCompleted: 2,
    totalLearningTime: 7200, // 120 hours in minutes
    streakDays: 7,
    averageScore: 85,
    totalPoints: 2500,
    level: 3,
  },
  learningStreak: {
    current: 7,
    longest: 15,
  },
  weeklyGoal: {
    target: 300,
    current: 180,
    percentage: 60,
  },
  recentProgress: [
    {
      lesson: {
        title: 'Introduction to React',
        course: {
          title: 'React Fundamentals',
        },
      },
      timeSpent: 45,
      isCompleted: true,
      lastAccessed: '2024-01-15T10:00:00Z',
    },
    {
      lesson: {
        title: 'JavaScript Basics',
        course: {
          title: 'Web Development Bootcamp',
        },
      },
      timeSpent: 30,
      isCompleted: false,
      lastAccessed: '2024-01-14T15:30:00Z',
    },
  ],
  achievements: [
    {
      id: '1',
      name: 'First Course Completed',
      description: 'Complete your first course',
      icon: 'trophy',
      earnedAt: '2024-01-10T12:00:00Z',
    },
    {
      id: '2',
      name: 'Week Streak',
      description: 'Learn for 7 consecutive days',
      icon: 'fire',
      earnedAt: '2024-01-15T09:00:00Z',
    },
  ],
  performanceTrend: [
    { date: '2024-01-08', score: 75 },
    { date: '2024-01-09', score: 80 },
    { date: '2024-01-10', score: 85 },
    { date: '2024-01-11', score: 82 },
    { date: '2024-01-12', score: 88 },
  ],
}

describe('StudentDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue('mock-token')
  })

  it('renders loading state initially', () => {
    ;(fetch as jest.Mock).mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<StudentDashboard />)

    expect(screen.getByRole('status')).toBeInTheDocument()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders dashboard data successfully', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Welcome back!')).toBeInTheDocument()
    })

    // Check stats cards
    expect(screen.getByText('5')).toBeInTheDocument() // Courses enrolled
    expect(screen.getByText('2 completed')).toBeInTheDocument()
    expect(screen.getByText('120h')).toBeInTheDocument() // Learning time
    expect(screen.getByText('7')).toBeInTheDocument() // Current streak
    expect(screen.getByText('85%')).toBeInTheDocument() // Average score

    // Check weekly goal
    expect(screen.getByText('Weekly Goal')).toBeInTheDocument()
    expect(screen.getByText('180 / 300 minutes')).toBeInTheDocument()
    expect(screen.getByText('60% complete')).toBeInTheDocument()

    // Check level progress
    expect(screen.getByText('Level 3')).toBeInTheDocument()
    expect(screen.getByText('2500 points earned')).toBeInTheDocument()

    // Check recent achievements
    expect(screen.getByText('Recent Achievements')).toBeInTheDocument()
    expect(screen.getByText('First Course Completed')).toBeInTheDocument()
    expect(screen.getByText('Week Streak')).toBeInTheDocument()
  })

  it('renders recent progress correctly', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Recent Learning Activity')).toBeInTheDocument()
    })

    // Check progress items
    expect(screen.getByText('Introduction to React')).toBeInTheDocument()
    expect(screen.getByText('React Fundamentals')).toBeInTheDocument()
    expect(screen.getByText('45 min')).toBeInTheDocument()

    expect(screen.getByText('JavaScript Basics')).toBeInTheDocument()
    expect(screen.getByText('Web Development Bootcamp')).toBeInTheDocument()
    expect(screen.getByText('30 min')).toBeInTheDocument()
  })

  it('renders performance trend chart', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Performance Trend')).toBeInTheDocument()
    })

    // Check if chart components are rendered
    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
  })

  it('handles API error gracefully', async () => {
    ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'))

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Failed to load dashboard data')).toBeInTheDocument()
    })
  })

  it('handles empty response gracefully', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500,
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Failed to load dashboard data')).toBeInTheDocument()
    })
  })

  it('renders quick actions', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
    })

    expect(screen.getByText('Continue Learning')).toBeInTheDocument()
    expect(screen.getByText('Browse Courses')).toBeInTheDocument()
    expect(screen.getByText('View Achievements')).toBeInTheDocument()
  })

  it('switches between tabs correctly', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Recent Progress')).toBeInTheDocument()
    })

    // Check default tab
    expect(screen.getByText('Recent Learning Activity')).toBeInTheDocument()

    // Click performance tab
    const performanceTab = screen.getByRole('tab', { name: 'Performance' })
    performanceTab.click()

    expect(screen.getByText('Performance Trend')).toBeInTheDocument()
    expect(screen.getByText('Your quiz scores over time')).toBeInTheDocument()

    // Click schedule tab
    const scheduleTab = screen.getByRole('tab', { name: 'Schedule' })
    scheduleTab.click()

    expect(screen.getByText('Learning Schedule')).toBeInTheDocument()
    expect(screen.getByText('Schedule feature coming soon')).toBeInTheDocument()
  })

  it('makes correct API call', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/analytics/my/dashboard', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      })
    })
  })

  it('displays correct progress indicators', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockDashboardData,
      }),
    })

    render(<StudentDashboard />)

    await waitFor(() => {
      // Check for completed lesson indicator (green dot)
      const progressItems = screen.getAllByRole('listitem')
      expect(progressItems.length).toBeGreaterThan(0)
    })
  })
})

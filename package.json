{"name": "adaptive-elearning-platform", "version": "1.0.0", "description": "A comprehensive adaptive e-learning platform with personalized learning paths", "main": "index.js", "scripts": {"install:all": "npm install && cd client && npm install && cd ../server && npm install", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "concurrently \"npm run start:server\" \"npm run start:client\"", "start:client": "cd client && npm start", "start:server": "cd server && npm start", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "lint:fix": "npm run lint:client -- --fix && npm run lint:server -- --fix", "test": "npm run test:server && npm run test:client", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "test:watch": "concurrently \"npm run test:server -- --watch\" \"npm run test:client -- --watch\"", "test:coverage": "npm run test:server -- --coverage && npm run test:client -- --coverage", "db:migrate": "cd server && npx prisma migrate dev", "db:seed": "cd server && npx prisma db seed", "db:studio": "cd server && npx prisma studio", "db:reset": "cd server && npx prisma migrate reset", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "prepare": "husky install"}, "keywords": ["e-learning", "adaptive-learning", "education", "nextjs", "express", "typescript", "postgresql", "redis", "prisma"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "lint-staged": {"client/**/*.{js,jsx,ts,tsx}": ["cd client && npm run lint:fix", "cd client && npm run type-check"], "server/**/*.{js,ts}": ["cd server && npm run lint:fix"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/adaptive-elearning-platform.git"}, "bugs": {"url": "https://github.com/yourusername/adaptive-elearning-platform/issues"}, "homepage": "https://github.com/yourusername/adaptive-elearning-platform#readme"}
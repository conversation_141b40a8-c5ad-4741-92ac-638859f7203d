import request from 'supertest'
import { app } from '../app'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

describe('Course Endpoints', () => {
  let studentToken: string
  let instructorToken: string
  let adminToken: string
  let courseId: string
  let studentId: string
  let instructorId: string

  beforeAll(async () => {
    // Clean up test database
    await prisma.enrollment.deleteMany()
    await prisma.lesson.deleteMany()
    await prisma.course.deleteMany()
    await prisma.user.deleteMany()

    // Create test users
    const hashedPassword = await bcrypt.hash('password123', 12)

    const student = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Student',
        lastName: 'User',
        role: 'STUDENT',
        isVerified: true,
        isActive: true,
      }
    })
    studentId = student.id

    const instructor = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Instructor',
        lastName: 'User',
        role: 'INSTRUCTOR',
        isVerified: true,
        isActive: true,
      }
    })
    instructorId = instructor.id

    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        isVerified: true,
        isActive: true,
      }
    })

    // Get tokens for each user
    const studentLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
    studentToken = studentLogin.body.data.tokens.accessToken

    const instructorLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
    instructorToken = instructorLogin.body.data.tokens.accessToken

    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
    adminToken = adminLogin.body.data.tokens.accessToken
  })

  afterAll(async () => {
    // Clean up after tests
    await prisma.enrollment.deleteMany()
    await prisma.lesson.deleteMany()
    await prisma.course.deleteMany()
    await prisma.user.deleteMany()
    await prisma.$disconnect()
  })

  describe('POST /api/courses', () => {
    it('should create a course as instructor', async () => {
      const courseData = {
        title: 'Test Course',
        description: 'This is a test course description',
        category: 'Programming',
        level: 'BEGINNER',
        duration: 120,
        price: 99.99,
        tags: ['javascript', 'web development']
      }

      const response = await request(app)
        .post('/api/courses')
        .set('Authorization', `Bearer ${instructorToken}`)
        .send(courseData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Course created successfully')
      expect(response.body.data.title).toBe(courseData.title)
      expect(response.body.data.description).toBe(courseData.description)
      expect(response.body.data.category).toBe(courseData.category)
      expect(response.body.data.level).toBe(courseData.level)
      expect(response.body.data.duration).toBe(courseData.duration)
      expect(response.body.data.price).toBe(courseData.price)
      expect(response.body.data.instructorId).toBe(instructorId)

      courseId = response.body.data.id
    })

    it('should not create course as student', async () => {
      const courseData = {
        title: 'Unauthorized Course',
        description: 'This should not be created',
        category: 'Programming',
        level: 'BEGINNER',
        duration: 60,
        price: 49.99
      }

      const response = await request(app)
        .post('/api/courses')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(courseData)
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Access denied')
    })

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/courses')
        .set('Authorization', `Bearer ${instructorToken}`)
        .send({
          title: 'A', // Too short
          description: 'Short', // Too short
          category: '',
          level: 'INVALID',
          duration: -1,
          price: -10
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Validation failed')
      expect(response.body.errors).toBeDefined()
    })
  })

  describe('GET /api/courses', () => {
    beforeAll(async () => {
      // Publish the course
      await prisma.course.update({
        where: { id: courseId },
        data: { isPublished: true }
      })
    })

    it('should get all published courses', async () => {
      const response = await request(app)
        .get('/api/courses')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.courses).toBeDefined()
      expect(response.body.data.courses.length).toBeGreaterThan(0)
      expect(response.body.data.pagination).toBeDefined()
    })

    it('should filter courses by category', async () => {
      const response = await request(app)
        .get('/api/courses?category=Programming')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.courses).toBeDefined()
      response.body.data.courses.forEach((course: any) => {
        expect(course.category).toBe('Programming')
      })
    })

    it('should filter courses by level', async () => {
      const response = await request(app)
        .get('/api/courses?level=BEGINNER')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.courses).toBeDefined()
      response.body.data.courses.forEach((course: any) => {
        expect(course.level).toBe('BEGINNER')
      })
    })

    it('should search courses by title', async () => {
      const response = await request(app)
        .get('/api/courses?search=Test')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.courses).toBeDefined()
    })

    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api/courses?page=1&limit=5')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.pagination.page).toBe(1)
      expect(response.body.data.pagination.limit).toBe(5)
    })
  })

  describe('GET /api/courses/:id', () => {
    it('should get course details', async () => {
      const response = await request(app)
        .get(`/api/courses/${courseId}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe(courseId)
      expect(response.body.data.title).toBe('Test Course')
      expect(response.body.data.instructor).toBeDefined()
      expect(response.body.data.lessons).toBeDefined()
    })

    it('should return 404 for non-existent course', async () => {
      const response = await request(app)
        .get('/api/courses/non-existent-id')
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Course not found')
    })
  })

  describe('PUT /api/courses/:id', () => {
    it('should update course as instructor', async () => {
      const updateData = {
        title: 'Updated Test Course',
        description: 'Updated description',
        price: 149.99
      }

      const response = await request(app)
        .put(`/api/courses/${courseId}`)
        .set('Authorization', `Bearer ${instructorToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Course updated successfully')
      expect(response.body.data.title).toBe(updateData.title)
      expect(response.body.data.description).toBe(updateData.description)
      expect(response.body.data.price).toBe(updateData.price)
    })

    it('should not update course as student', async () => {
      const response = await request(app)
        .put(`/api/courses/${courseId}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .send({ title: 'Unauthorized Update' })
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Access denied')
    })
  })

  describe('POST /api/courses/:id/enroll', () => {
    it('should enroll student in course', async () => {
      const response = await request(app)
        .post(`/api/courses/${courseId}/enroll`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Successfully enrolled in course')
    })

    it('should not enroll in same course twice', async () => {
      const response = await request(app)
        .post(`/api/courses/${courseId}/enroll`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Already enrolled in this course')
    })

    it('should not enroll without authentication', async () => {
      const response = await request(app)
        .post(`/api/courses/${courseId}/enroll`)
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Access token is required')
    })
  })

  describe('GET /api/courses/:id/enrollment', () => {
    it('should get enrollment status for enrolled student', async () => {
      const response = await request(app)
        .get(`/api/courses/${courseId}/enrollment`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.isEnrolled).toBe(true)
      expect(response.body.data.enrollment).toBeDefined()
    })

    it('should get enrollment status for non-enrolled user', async () => {
      const response = await request(app)
        .get(`/api/courses/${courseId}/enrollment`)
        .set('Authorization', `Bearer ${instructorToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.isEnrolled).toBe(false)
      expect(response.body.data.enrollment).toBeNull()
    })
  })

  describe('DELETE /api/courses/:id/enroll', () => {
    it('should unenroll student from course', async () => {
      const response = await request(app)
        .delete(`/api/courses/${courseId}/enroll`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Successfully unenrolled from course')
    })

    it('should not unenroll from non-enrolled course', async () => {
      const response = await request(app)
        .delete(`/api/courses/${courseId}/enroll`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Enrollment not found')
    })
  })

  describe('GET /api/courses/my/enrolled', () => {
    beforeAll(async () => {
      // Re-enroll student for this test
      await request(app)
        .post(`/api/courses/${courseId}/enroll`)
        .set('Authorization', `Bearer ${studentToken}`)
    })

    it('should get enrolled courses for student', async () => {
      const response = await request(app)
        .get('/api/courses/my/enrolled')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(response.body.data.length).toBeGreaterThan(0)
      expect(response.body.data[0].course.title).toBe('Updated Test Course')
    })
  })

  describe('GET /api/courses/my/created', () => {
    it('should get created courses for instructor', async () => {
      const response = await request(app)
        .get('/api/courses/my/created')
        .set('Authorization', `Bearer ${instructorToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(response.body.data.length).toBeGreaterThan(0)
      expect(response.body.data[0].title).toBe('Updated Test Course')
    })

    it('should not allow student to access created courses', async () => {
      const response = await request(app)
        .get('/api/courses/my/created')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Access denied')
    })
  })

  describe('DELETE /api/courses/:id', () => {
    it('should not delete course as student', async () => {
      const response = await request(app)
        .delete(`/api/courses/${courseId}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Access denied')
    })

    it('should delete course as instructor', async () => {
      const response = await request(app)
        .delete(`/api/courses/${courseId}`)
        .set('Authorization', `Bearer ${instructorToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Course deleted successfully')
    })

    it('should return 404 for deleted course', async () => {
      const response = await request(app)
        .get(`/api/courses/${courseId}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBe('Course not found')
    })
  })
})

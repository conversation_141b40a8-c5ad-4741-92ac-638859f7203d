import nodemailer from 'nodemailer'
import { logger } from './logger'

interface EmailOptions {
  to: string
  subject: string
  template: string
  data: Record<string, any>
}

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  })
}

// Email templates
const templates = {
  welcome: (data: any) => ({
    subject: 'Welcome to Adaptive E-Learning Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to Adaptive E-Learning Platform!</h1>
        <p>Hi ${data.firstName},</p>
        <p>Welcome to our adaptive learning platform! We're excited to have you join our community of learners.</p>
        <p>To get started, please verify your email address by clicking the button below:</p>
        <a href="${data.verificationUrl}" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Verify Email Address
        </a>
        <p>If you didn't create this account, please ignore this email.</p>
        <p>Best regards,<br>The Adaptive E-Learning Team</p>
      </div>
    `,
  }),

  'password-reset': (data: any) => ({
    subject: 'Password Reset Request',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Password Reset Request</h1>
        <p>Hi ${data.firstName},</p>
        <p>We received a request to reset your password. Click the button below to reset it:</p>
        <a href="${data.resetUrl}" style="display: inline-block; background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Reset Password
        </a>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
        <p>Best regards,<br>The Adaptive E-Learning Team</p>
      </div>
    `,
  }),

  'email-verification': (data: any) => ({
    subject: 'Verify Your Email Address',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Verify Your Email Address</h1>
        <p>Hi ${data.firstName},</p>
        <p>Please verify your email address by clicking the button below:</p>
        <a href="${data.verificationUrl}" style="display: inline-block; background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Verify Email
        </a>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't create this account, please ignore this email.</p>
        <p>Best regards,<br>The Adaptive E-Learning Team</p>
      </div>
    `,
  }),

  'course-enrollment': (data: any) => ({
    subject: `You've been enrolled in ${data.courseName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Course Enrollment Confirmation</h1>
        <p>Hi ${data.firstName},</p>
        <p>You have been successfully enrolled in <strong>${data.courseName}</strong>!</p>
        <p>You can start learning right away by accessing your course dashboard.</p>
        <a href="${data.courseUrl}" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Start Learning
        </a>
        <p>Happy learning!</p>
        <p>Best regards,<br>The Adaptive E-Learning Team</p>
      </div>
    `,
  }),

  'assignment-due': (data: any) => ({
    subject: `Assignment Due: ${data.assignmentName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #f59e0b;">Assignment Due Reminder</h1>
        <p>Hi ${data.firstName},</p>
        <p>This is a reminder that your assignment <strong>${data.assignmentName}</strong> is due on ${data.dueDate}.</p>
        <p>Don't forget to submit your work before the deadline!</p>
        <a href="${data.assignmentUrl}" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          View Assignment
        </a>
        <p>Good luck!</p>
        <p>Best regards,<br>The Adaptive E-Learning Team</p>
      </div>
    `,
  }),
}

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    const transporter = createTransporter()
    
    const template = templates[options.template as keyof typeof templates]
    if (!template) {
      throw new Error(`Email template '${options.template}' not found`)
    }

    const { subject, html } = template(options.data)

    const mailOptions = {
      from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
      to: options.to,
      subject: options.subject || subject,
      html,
    }

    await transporter.sendMail(mailOptions)
    logger.info(`Email sent successfully to ${options.to}`)
  } catch (error) {
    logger.error('Failed to send email:', error)
    throw error
  }
}

export const sendBulkEmail = async (recipients: string[], options: Omit<EmailOptions, 'to'>): Promise<void> => {
  try {
    const promises = recipients.map(recipient => 
      sendEmail({ ...options, to: recipient })
    )
    
    await Promise.all(promises)
    logger.info(`Bulk email sent to ${recipients.length} recipients`)
  } catch (error) {
    logger.error('Failed to send bulk email:', error)
    throw error
  }
}

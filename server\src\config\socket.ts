import { Server as SocketIOServer } from 'socket.io'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export const setupSocketIO = (io: SocketIOServer) => {
  // Authentication middleware for Socket.IO
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'))
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
        },
      })

      if (!user || !user.isActive) {
        return next(new Error('Authentication error: User not found or inactive'))
      }

      socket.data.user = user
      next()
    } catch (error) {
      logger.error('Socket authentication error:', error)
      next(new Error('Authentication error'))
    }
  })

  io.on('connection', (socket) => {
    const user = socket.data.user
    logger.info(`User ${user.email} connected via Socket.IO`)

    // Join user to their personal room
    socket.join(`user:${user.id}`)

    // Handle joining course rooms
    socket.on('join_course', async (courseId: string) => {
      try {
        // Verify user has access to the course
        const enrollment = await prisma.enrollment.findUnique({
          where: {
            userId_courseId: {
              userId: user.id,
              courseId: courseId,
            },
          },
        })

        const course = await prisma.course.findUnique({
          where: { id: courseId },
          select: { instructorId: true },
        })

        if (enrollment || course?.instructorId === user.id || user.role === 'ADMIN') {
          socket.join(`course:${courseId}`)
          socket.emit('joined_course', { courseId })
          logger.info(`User ${user.email} joined course room: ${courseId}`)
        } else {
          socket.emit('error', { message: 'Access denied to course' })
        }
      } catch (error) {
        logger.error('Error joining course room:', error)
        socket.emit('error', { message: 'Failed to join course' })
      }
    })

    // Handle leaving course rooms
    socket.on('leave_course', (courseId: string) => {
      socket.leave(`course:${courseId}`)
      socket.emit('left_course', { courseId })
      logger.info(`User ${user.email} left course room: ${courseId}`)
    })

    // Handle real-time quiz participation
    socket.on('join_quiz', async (quizId: string) => {
      try {
        const quiz = await prisma.quiz.findUnique({
          where: { id: quizId },
          include: { course: true, lesson: true },
        })

        if (!quiz) {
          socket.emit('error', { message: 'Quiz not found' })
          return
        }

        // Check if user has access to the quiz
        let hasAccess = false

        if (quiz.courseId) {
          const enrollment = await prisma.enrollment.findUnique({
            where: {
              userId_courseId: {
                userId: user.id,
                courseId: quiz.courseId,
              },
            },
          })
          hasAccess = !!enrollment || quiz.course?.instructorId === user.id
        }

        if (hasAccess || user.role === 'ADMIN') {
          socket.join(`quiz:${quizId}`)
          socket.emit('joined_quiz', { quizId })
          
          // Notify others in the quiz room
          socket.to(`quiz:${quizId}`).emit('user_joined_quiz', {
            userId: user.id,
            userName: `${user.firstName} ${user.lastName}`,
          })
        } else {
          socket.emit('error', { message: 'Access denied to quiz' })
        }
      } catch (error) {
        logger.error('Error joining quiz room:', error)
        socket.emit('error', { message: 'Failed to join quiz' })
      }
    })

    // Handle quiz answer submission
    socket.on('submit_quiz_answer', (data: { quizId: string; questionId: string; answer: string }) => {
      // Broadcast to quiz room (for real-time quiz features)
      socket.to(`quiz:${data.quizId}`).emit('quiz_answer_submitted', {
        userId: user.id,
        questionId: data.questionId,
        timestamp: new Date(),
      })
    })

    // Handle discussion/chat messages
    socket.on('send_message', async (data: { courseId: string; message: string; type: 'discussion' | 'chat' }) => {
      try {
        // Verify user has access to the course
        const enrollment = await prisma.enrollment.findUnique({
          where: {
            userId_courseId: {
              userId: user.id,
              courseId: data.courseId,
            },
          },
        })

        const course = await prisma.course.findUnique({
          where: { id: data.courseId },
          select: { instructorId: true },
        })

        if (enrollment || course?.instructorId === user.id || user.role === 'ADMIN') {
          // Save message to database if it's a discussion post
          if (data.type === 'discussion') {
            await prisma.discussionPost.create({
              data: {
                content: data.message,
                userId: user.id,
                courseId: data.courseId,
              },
            })
          }

          // Broadcast message to course room
          io.to(`course:${data.courseId}`).emit('new_message', {
            id: Date.now().toString(),
            message: data.message,
            type: data.type,
            user: {
              id: user.id,
              name: `${user.firstName} ${user.lastName}`,
              avatar: null, // Add avatar if available
            },
            timestamp: new Date(),
          })
        } else {
          socket.emit('error', { message: 'Access denied to course' })
        }
      } catch (error) {
        logger.error('Error sending message:', error)
        socket.emit('error', { message: 'Failed to send message' })
      }
    })

    // Handle typing indicators
    socket.on('typing_start', (data: { courseId: string }) => {
      socket.to(`course:${data.courseId}`).emit('user_typing', {
        userId: user.id,
        userName: `${user.firstName} ${user.lastName}`,
      })
    })

    socket.on('typing_stop', (data: { courseId: string }) => {
      socket.to(`course:${data.courseId}`).emit('user_stopped_typing', {
        userId: user.id,
      })
    })

    // Handle progress updates
    socket.on('update_progress', async (data: { lessonId: string; progress: number }) => {
      try {
        await prisma.progress.upsert({
          where: {
            userId_lessonId: {
              userId: user.id,
              lessonId: data.lessonId,
            },
          },
          update: {
            timeSpent: data.progress,
            lastAccessed: new Date(),
          },
          create: {
            userId: user.id,
            lessonId: data.lessonId,
            timeSpent: data.progress,
            lastAccessed: new Date(),
          },
        })

        // Emit progress update to user's personal room
        socket.emit('progress_updated', {
          lessonId: data.lessonId,
          progress: data.progress,
        })
      } catch (error) {
        logger.error('Error updating progress:', error)
        socket.emit('error', { message: 'Failed to update progress' })
      }
    })

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User ${user.email} disconnected: ${reason}`)
    })

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to Adaptive E-Learning Platform',
      user: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        role: user.role,
      },
    })
  })

  // Utility functions for sending notifications
  const sendNotificationToUser = (userId: string, notification: any) => {
    io.to(`user:${userId}`).emit('notification', notification)
  }

  const sendNotificationToCourse = (courseId: string, notification: any) => {
    io.to(`course:${courseId}`).emit('notification', notification)
  }

  const sendBroadcastNotification = (notification: any) => {
    io.emit('notification', notification)
  }

  // Export utility functions
  return {
    sendNotificationToUser,
    sendNotificationToCourse,
    sendBroadcastNotification,
  }
}

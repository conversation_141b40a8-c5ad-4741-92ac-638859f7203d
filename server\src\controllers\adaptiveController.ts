import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'
import { AdaptiveLearningEngine } from '@/services/AdaptiveLearningEngine'
import { LearningAnalytics } from '@/services/LearningAnalytics'

const prisma = new PrismaClient()
const adaptiveEngine = new AdaptiveLearningEngine()
const learningAnalytics = new LearningAnalytics()

export const adaptiveController = {
  // Get personalized recommendations
  async getRecommendations(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      // Get user's learning data
      const userData = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          preferences: true,
          stats: true,
          adaptiveData: true,
          enrollments: {
            include: {
              course: {
                include: {
                  lessons: true,
                },
              },
            },
          },
          progress: {
            include: {
              lesson: {
                include: {
                  course: true,
                },
              },
            },
          },
          quizAttempts: {
            include: {
              quiz: {
                include: {
                  course: true,
                },
              },
            },
          },
        },
      })

      if (!userData) {
        throw new AppError('User not found', 404)
      }

      // Generate recommendations using adaptive engine
      const recommendations = await adaptiveEngine.generateRecommendations(userData)

      res.json({
        success: true,
        data: recommendations,
      })
    } catch (error) {
      logger.error('Get recommendations error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get user's learning path
  async getLearningPath(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const learningPaths = await prisma.learningPath.findMany({
        where: {
          userId,
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
      })

      res.json({
        success: true,
        data: learningPaths,
      })
    } catch (error) {
      logger.error('Get learning path error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Generate new learning path
  async generateLearningPath(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { courseId, goals, preferences } = req.body

      // Get user data for path generation
      const userData = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          preferences: true,
          stats: true,
          adaptiveData: true,
          progress: {
            include: {
              lesson: true,
            },
          },
          quizAttempts: {
            include: {
              quiz: true,
            },
          },
        },
      })

      if (!userData) {
        throw new AppError('User not found', 404)
      }

      // Generate learning path using adaptive engine
      const pathData = await adaptiveEngine.generateLearningPath(userData, {
        courseId,
        goals,
        preferences,
      })

      // Save learning path
      const learningPath = await prisma.learningPath.create({
        data: {
          userId,
          courseId,
          path: pathData,
          isActive: true,
        },
      })

      res.status(201).json({
        success: true,
        message: 'Learning path generated successfully',
        data: learningPath,
      })
    } catch (error) {
      logger.error('Generate learning path error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update learning path
  async updateLearningPath(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { pathId, updates } = req.body

      const learningPath = await prisma.learningPath.findFirst({
        where: {
          id: pathId,
          userId,
        },
      })

      if (!learningPath) {
        throw new AppError('Learning path not found', 404)
      }

      const updatedPath = await prisma.learningPath.update({
        where: { id: pathId },
        data: {
          path: updates.path || learningPath.path,
          isActive: updates.isActive !== undefined ? updates.isActive : learningPath.isActive,
        },
      })

      res.json({
        success: true,
        message: 'Learning path updated successfully',
        data: updatedPath,
      })
    } catch (error) {
      logger.error('Update learning path error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get learning patterns analysis
  async getLearningPatterns(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      // Get user's learning data
      const learningData = await learningAnalytics.getUserLearningData(userId)
      
      // Analyze learning patterns
      const patterns = await learningAnalytics.analyzeLearningPatterns(learningData)

      res.json({
        success: true,
        data: patterns,
      })
    } catch (error) {
      logger.error('Get learning patterns error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get strengths and weaknesses analysis
  async getStrengthsWeaknesses(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      // Get adaptive data
      const adaptiveData = await prisma.adaptiveData.findUnique({
        where: { userId },
      })

      if (!adaptiveData) {
        // Generate initial analysis
        const learningData = await learningAnalytics.getUserLearningData(userId)
        const analysis = await learningAnalytics.analyzeStrengthsWeaknesses(learningData)

        // Save to database
        const newAdaptiveData = await prisma.adaptiveData.create({
          data: {
            userId,
            learningPatterns: {},
            strengths: analysis.strengths,
            weaknesses: analysis.weaknesses,
            recommendedPace: analysis.recommendedPace || 'NORMAL',
            nextRecommendations: analysis.recommendations || [],
          },
        })

        res.json({
          success: true,
          data: {
            strengths: newAdaptiveData.strengths,
            weaknesses: newAdaptiveData.weaknesses,
            recommendations: newAdaptiveData.nextRecommendations,
          },
        })
      } else {
        res.json({
          success: true,
          data: {
            strengths: adaptiveData.strengths,
            weaknesses: adaptiveData.weaknesses,
            recommendations: adaptiveData.nextRecommendations,
          },
        })
      }
    } catch (error) {
      logger.error('Get strengths weaknesses error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get difficulty adjustment recommendations
  async getDifficultyAdjustment(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { courseId, lessonId } = req.query

      // Get user performance data
      const performanceData = await learningAnalytics.getUserPerformanceData(userId, {
        courseId: courseId as string,
        lessonId: lessonId as string,
      })

      // Calculate difficulty adjustment
      const adjustment = await adaptiveEngine.calculateDifficultyAdjustment(performanceData)

      res.json({
        success: true,
        data: adjustment,
      })
    } catch (error) {
      logger.error('Get difficulty adjustment error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get next recommended content
  async getNextContent(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { courseId } = req.query

      // Get user's current progress and preferences
      const userData = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          preferences: true,
          adaptiveData: true,
          progress: {
            where: courseId ? {
              lesson: {
                courseId: courseId as string,
              },
            } : undefined,
            include: {
              lesson: {
                include: {
                  course: true,
                },
              },
            },
          },
        },
      })

      if (!userData) {
        throw new AppError('User not found', 404)
      }

      // Get next content recommendation
      const nextContent = await adaptiveEngine.getNextContent(userData, courseId as string)

      res.json({
        success: true,
        data: nextContent,
      })
    } catch (error) {
      logger.error('Get next content error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Submit content feedback for adaptive learning
  async submitContentFeedback(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { contentId, contentType, feedback, difficulty, engagement } = req.body

      // Process feedback for adaptive learning
      await adaptiveEngine.processFeedback(userId, {
        contentId,
        contentType,
        feedback,
        difficulty,
        engagement,
      })

      // Update adaptive data
      const adaptiveData = await prisma.adaptiveData.findUnique({
        where: { userId },
      })

      if (adaptiveData) {
        // Update learning patterns based on feedback
        const updatedPatterns = await learningAnalytics.updateLearningPatterns(
          adaptiveData.learningPatterns as any,
          {
            contentId,
            contentType,
            feedback,
            difficulty,
            engagement,
          }
        )

        await prisma.adaptiveData.update({
          where: { userId },
          data: {
            learningPatterns: updatedPatterns,
            lastAnalyzed: new Date(),
          },
        })
      }

      res.json({
        success: true,
        message: 'Feedback submitted successfully',
      })
    } catch (error) {
      logger.error('Submit content feedback error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },
}

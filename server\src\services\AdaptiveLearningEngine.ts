import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

interface UserLearningData {
  id: string
  preferences: any
  stats: any
  adaptiveData: any
  enrollments: any[]
  progress: any[]
  quizAttempts: any[]
}

interface LearningPathOptions {
  courseId?: string
  goals?: string[]
  preferences?: any
}

interface ContentFeedback {
  contentId: string
  contentType: 'lesson' | 'quiz' | 'assignment'
  feedback: 'easy' | 'just_right' | 'difficult'
  difficulty: number // 1-5 scale
  engagement: number // 1-5 scale
}

export class AdaptiveLearningEngine {
  // Generate personalized recommendations
  async generateRecommendations(userData: UserLearningData) {
    try {
      const recommendations = {
        courses: await this.recommendCourses(userData),
        lessons: await this.recommendLessons(userData),
        reviewTopics: await this.identifyReviewTopics(userData),
        studyPlan: await this.generateStudyPlan(userData),
        difficultyAdjustments: await this.suggestDifficultyAdjustments(userData),
      }

      return recommendations
    } catch (error) {
      logger.error('Error generating recommendations:', error)
      throw error
    }
  }

  // Recommend courses based on user profile and progress
  private async recommendCourses(userData: UserLearningData) {
    const userPreferences = userData.preferences
    const userStats = userData.stats
    const completedCourses = userData.enrollments
      .filter((e: any) => e.status === 'COMPLETED')
      .map((e: any) => e.course)

    // Get courses similar to completed ones
    const categories = completedCourses.map((c: any) => c.category)
    const preferredLevel = this.calculatePreferredLevel(userData)

    const recommendedCourses = await prisma.course.findMany({
      where: {
        isPublished: true,
        category: categories.length > 0 ? { in: categories } : undefined,
        level: preferredLevel,
        id: {
          notIn: userData.enrollments.map((e: any) => e.courseId),
        },
      },
      include: {
        instructor: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        _count: {
          select: {
            enrollments: true,
            reviews: true,
          },
        },
        reviews: {
          select: {
            rating: true,
          },
        },
      },
      take: 10,
      orderBy: [
        { enrollments: { _count: 'desc' } },
        { createdAt: 'desc' },
      ],
    })

    return recommendedCourses.map(course => ({
      ...course,
      recommendationScore: this.calculateCourseRecommendationScore(course, userData),
      reason: this.generateRecommendationReason(course, userData),
    }))
  }

  // Recommend next lessons based on learning path
  private async recommendLessons(userData: UserLearningData) {
    const enrolledCourses = userData.enrollments.filter((e: any) => e.status === 'ACTIVE')
    const recommendations = []

    for (const enrollment of enrolledCourses) {
      const courseProgress = userData.progress.filter(
        (p: any) => p.lesson.courseId === enrollment.courseId
      )

      const completedLessonIds = courseProgress
        .filter((p: any) => p.isCompleted)
        .map((p: any) => p.lessonId)

      const nextLessons = await prisma.lesson.findMany({
        where: {
          courseId: enrollment.courseId,
          isPublished: true,
          id: { notIn: completedLessonIds },
        },
        orderBy: { order: 'asc' },
        take: 3,
      })

      recommendations.push({
        courseId: enrollment.courseId,
        courseTitle: enrollment.course.title,
        lessons: nextLessons.map(lesson => ({
          ...lesson,
          priority: this.calculateLessonPriority(lesson, userData),
          estimatedDifficulty: this.estimateLessonDifficulty(lesson, userData),
        })),
      })
    }

    return recommendations
  }

  // Identify topics that need review
  private async identifyReviewTopics(userData: UserLearningData) {
    const weakPerformanceThreshold = 70 // percentage

    const reviewTopics = []

    // Analyze quiz performance
    for (const attempt of userData.quizAttempts) {
      if (attempt.score < weakPerformanceThreshold) {
        const quiz = attempt.quiz
        reviewTopics.push({
          type: 'quiz',
          id: quiz.id,
          title: quiz.title,
          courseId: quiz.courseId,
          score: attempt.score,
          reason: 'Low quiz score',
          priority: this.calculateReviewPriority(attempt.score),
        })
      }
    }

    // Analyze lesson completion time vs average
    const lessonTimes = userData.progress.map((p: any) => ({
      lessonId: p.lessonId,
      timeSpent: p.timeSpent,
      lesson: p.lesson,
    }))

    for (const lessonTime of lessonTimes) {
      const avgTime = await this.getAverageLessonTime(lessonTime.lessonId)
      if (lessonTime.timeSpent > avgTime * 1.5) {
        reviewTopics.push({
          type: 'lesson',
          id: lessonTime.lessonId,
          title: lessonTime.lesson.title,
          courseId: lessonTime.lesson.courseId,
          reason: 'Took longer than average',
          priority: 'medium',
        })
      }
    }

    return reviewTopics.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority as keyof typeof priorityOrder] - 
             priorityOrder[a.priority as keyof typeof priorityOrder]
    })
  }

  // Generate personalized study plan
  private async generateStudyPlan(userData: UserLearningData) {
    const userPreferences = userData.preferences
    const availableTime = this.estimateAvailableStudyTime(userData)
    const learningPace = userPreferences?.pace || 'NORMAL'

    const studyPlan = {
      dailyGoal: this.calculateDailyGoal(availableTime, learningPace),
      weeklySchedule: await this.generateWeeklySchedule(userData),
      prioritizedContent: await this.prioritizeContent(userData),
      milestones: await this.generateMilestones(userData),
    }

    return studyPlan
  }

  // Suggest difficulty adjustments
  private async suggestDifficultyAdjustments(userData: UserLearningData) {
    const recentPerformance = await this.analyzeRecentPerformance(userData)
    const adjustments = []

    if (recentPerformance.averageScore > 90) {
      adjustments.push({
        type: 'increase_difficulty',
        reason: 'High performance indicates content may be too easy',
        suggestion: 'Consider advanced topics or faster pace',
      })
    } else if (recentPerformance.averageScore < 60) {
      adjustments.push({
        type: 'decrease_difficulty',
        reason: 'Low performance indicates content may be too difficult',
        suggestion: 'Review fundamentals or slow down pace',
      })
    }

    if (recentPerformance.completionTime > recentPerformance.averageTime * 1.3) {
      adjustments.push({
        type: 'slow_pace',
        reason: 'Taking longer than average to complete content',
        suggestion: 'Consider reducing daily goals or adding review time',
      })
    }

    return adjustments
  }

  // Generate learning path for a specific course or goal
  async generateLearningPath(userData: UserLearningData, options: LearningPathOptions) {
    const { courseId, goals, preferences } = options

    if (courseId) {
      return await this.generateCourseLearningPath(userData, courseId)
    } else {
      return await this.generateGoalBasedLearningPath(userData, goals || [])
    }
  }

  // Generate course-specific learning path
  private async generateCourseLearningPath(userData: UserLearningData, courseId: string) {
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: {
        lessons: {
          where: { isPublished: true },
          orderBy: { order: 'asc' },
        },
        quizzes: {
          where: { isPublished: true },
        },
      },
    })

    if (!course) {
      throw new Error('Course not found')
    }

    const userProgress = userData.progress.filter(
      (p: any) => p.lesson.courseId === courseId
    )

    const completedLessonIds = userProgress
      .filter((p: any) => p.isCompleted)
      .map((p: any) => p.lessonId)

    const path = []

    // Add lessons in adaptive order
    for (const lesson of course.lessons) {
      if (!completedLessonIds.includes(lesson.id)) {
        path.push({
          type: 'lesson',
          id: lesson.id,
          title: lesson.title,
          estimatedTime: lesson.duration,
          difficulty: await this.estimateLessonDifficulty(lesson, userData),
          prerequisites: await this.getLessonPrerequisites(lesson.id),
        })

        // Add quiz after lesson if available
        const relatedQuiz = course.quizzes.find(q => q.lessonId === lesson.id)
        if (relatedQuiz) {
          path.push({
            type: 'quiz',
            id: relatedQuiz.id,
            title: relatedQuiz.title,
            estimatedTime: relatedQuiz.timeLimit || 30,
            difficulty: 'medium',
          })
        }
      }
    }

    return {
      courseId,
      path,
      estimatedCompletionTime: path.reduce((sum, item) => sum + item.estimatedTime, 0),
      adaptiveFeatures: {
        difficultyAdjustment: true,
        paceAdaptation: true,
        contentRecommendation: true,
      },
    }
  }

  // Calculate difficulty adjustment based on performance
  async calculateDifficultyAdjustment(performanceData: any) {
    const { averageScore, completionTime, strugglingTopics } = performanceData

    let adjustment = 0

    // Score-based adjustment
    if (averageScore > 90) {
      adjustment += 0.2 // Increase difficulty
    } else if (averageScore < 60) {
      adjustment -= 0.3 // Decrease difficulty
    }

    // Time-based adjustment
    if (completionTime > performanceData.averageTime * 1.5) {
      adjustment -= 0.1 // Slightly decrease difficulty
    }

    // Topic-specific adjustments
    if (strugglingTopics.length > 3) {
      adjustment -= 0.2 // Decrease difficulty significantly
    }

    return {
      adjustment: Math.max(-0.5, Math.min(0.5, adjustment)),
      recommendations: this.generateDifficultyRecommendations(adjustment),
    }
  }

  // Get next content recommendation
  async getNextContent(userData: UserLearningData, courseId?: string) {
    const recommendations = await this.generateRecommendations(userData)
    
    if (courseId) {
      const courseRecommendations = recommendations.lessons.find(
        (r: any) => r.courseId === courseId
      )
      return courseRecommendations?.lessons[0] || null
    }

    // Return highest priority content across all courses
    const allLessons = recommendations.lessons.flatMap((r: any) => 
      r.lessons.map((lesson: any) => ({ ...lesson, courseId: r.courseId }))
    )

    return allLessons.sort((a, b) => b.priority - a.priority)[0] || null
  }

  // Process user feedback for adaptive learning
  async processFeedback(userId: string, feedback: ContentFeedback) {
    // Update user's adaptive data based on feedback
    const adaptiveData = await prisma.adaptiveData.findUnique({
      where: { userId },
    })

    if (adaptiveData) {
      const patterns = adaptiveData.learningPatterns as any || {}
      
      // Update difficulty preferences
      if (!patterns.difficultyPreferences) {
        patterns.difficultyPreferences = {}
      }
      
      patterns.difficultyPreferences[feedback.contentType] = {
        preferredDifficulty: feedback.difficulty,
        lastFeedback: feedback.feedback,
        engagement: feedback.engagement,
        updatedAt: new Date(),
      }

      await prisma.adaptiveData.update({
        where: { userId },
        data: {
          learningPatterns: patterns,
          lastAnalyzed: new Date(),
        },
      })
    }
  }

  // Helper methods
  private calculatePreferredLevel(userData: UserLearningData) {
    const userLevel = userData.preferences?.difficulty || 'BEGINNER'
    const completedCourses = userData.enrollments.filter((e: any) => e.status === 'COMPLETED')
    
    if (completedCourses.length > 5) {
      return 'ADVANCED'
    } else if (completedCourses.length > 2) {
      return 'INTERMEDIATE'
    }
    
    return userLevel
  }

  private calculateCourseRecommendationScore(course: any, userData: UserLearningData) {
    let score = 0
    
    // Base score from popularity
    score += Math.min(course._count.enrollments / 100, 1) * 0.3
    
    // Score from ratings
    if (course.reviews.length > 0) {
      const avgRating = course.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / course.reviews.length
      score += (avgRating / 5) * 0.3
    }
    
    // Score from user preferences
    const userPreferences = userData.preferences
    if (userPreferences?.learningStyle) {
      // Add logic for learning style matching
      score += 0.2
    }
    
    // Score from difficulty match
    if (course.level === userData.preferences?.difficulty) {
      score += 0.2
    }
    
    return Math.min(score, 1)
  }

  private generateRecommendationReason(course: any, userData: UserLearningData) {
    const reasons = []
    
    if (course._count.enrollments > 1000) {
      reasons.push('Popular course')
    }
    
    if (course.level === userData.preferences?.difficulty) {
      reasons.push('Matches your skill level')
    }
    
    const completedCategories = userData.enrollments
      .filter((e: any) => e.status === 'COMPLETED')
      .map((e: any) => e.course.category)
    
    if (completedCategories.includes(course.category)) {
      reasons.push('Similar to courses you\'ve completed')
    }
    
    return reasons.join(', ') || 'Recommended for you'
  }

  private calculateLessonPriority(lesson: any, userData: UserLearningData) {
    // Simple priority calculation based on order and user progress
    return 1 / (lesson.order + 1)
  }

  private async estimateLessonDifficulty(lesson: any, userData: UserLearningData) {
    // Estimate difficulty based on lesson content, user performance, etc.
    return 'medium' // Simplified for now
  }

  private calculateReviewPriority(score: number) {
    if (score < 50) return 'high'
    if (score < 70) return 'medium'
    return 'low'
  }

  private async getAverageLessonTime(lessonId: string) {
    const avgTime = await prisma.progress.aggregate({
      where: { lessonId },
      _avg: { timeSpent: true },
    })
    
    return avgTime._avg.timeSpent || 30 // Default 30 minutes
  }

  private estimateAvailableStudyTime(userData: UserLearningData) {
    // Estimate based on user activity patterns
    return 60 // Default 60 minutes per day
  }

  private calculateDailyGoal(availableTime: number, pace: string) {
    const paceMultiplier = {
      SLOW: 0.7,
      NORMAL: 1.0,
      FAST: 1.3,
    }
    
    return Math.round(availableTime * (paceMultiplier[pace as keyof typeof paceMultiplier] || 1))
  }

  private async generateWeeklySchedule(userData: UserLearningData) {
    // Generate a weekly study schedule based on user preferences
    return {
      monday: { duration: 60, focus: 'new_content' },
      tuesday: { duration: 45, focus: 'practice' },
      wednesday: { duration: 60, focus: 'new_content' },
      thursday: { duration: 30, focus: 'review' },
      friday: { duration: 60, focus: 'new_content' },
      saturday: { duration: 90, focus: 'project' },
      sunday: { duration: 30, focus: 'review' },
    }
  }

  private async prioritizeContent(userData: UserLearningData) {
    // Prioritize content based on deadlines, difficulty, and user goals
    return []
  }

  private async generateMilestones(userData: UserLearningData) {
    // Generate learning milestones
    return []
  }

  private async analyzeRecentPerformance(userData: UserLearningData) {
    const recentAttempts = userData.quizAttempts.slice(-10) // Last 10 attempts
    
    const averageScore = recentAttempts.length > 0
      ? recentAttempts.reduce((sum: number, attempt: any) => sum + attempt.score, 0) / recentAttempts.length
      : 0
    
    const averageTime = recentAttempts.length > 0
      ? recentAttempts.reduce((sum: number, attempt: any) => sum + attempt.timeSpent, 0) / recentAttempts.length
      : 0
    
    return {
      averageScore,
      averageTime,
      completionTime: averageTime,
    }
  }

  private async generateGoalBasedLearningPath(userData: UserLearningData, goals: string[]) {
    // Generate learning path based on specific goals
    return {
      goals,
      path: [],
      estimatedCompletionTime: 0,
    }
  }

  private async getLessonPrerequisites(lessonId: string) {
    // Get lesson prerequisites
    return []
  }

  private generateDifficultyRecommendations(adjustment: number) {
    if (adjustment > 0.2) {
      return ['Try advanced topics', 'Increase study pace', 'Take on challenging projects']
    } else if (adjustment < -0.2) {
      return ['Review fundamentals', 'Slow down pace', 'Focus on practice exercises']
    }
    
    return ['Continue current approach', 'Maintain steady progress']
  }
}

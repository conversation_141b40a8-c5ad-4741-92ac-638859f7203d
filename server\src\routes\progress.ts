import { Router } from 'express'
import { param, query } from 'express-validator'
import { progressController } from '@/controllers/progressController'
import { authMiddleware, authorize } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validateRequest'

const router = Router()

// All routes require authentication
router.use(authMiddleware)

// Student progress routes
router.get('/my', progressController.getMyProgress)
router.get('/my/courses/:courseId', validateRequest, progressController.getMyCourseProgress)
router.get('/my/stats', progressController.getMyStats)
router.get('/my/achievements', progressController.getMyAchievements)

// Course progress (for instructors and admins)
router.get('/course/:courseId', authorize('INSTRUCTOR', 'ADMIN'), validateRequest, progressController.getCourseProgress)
router.get('/course/:courseId/students', authorize('INSTRUCTOR', 'ADMIN'), validateRequest, progressController.getCourseStudentProgress)
router.get('/course/:courseId/analytics', authorize('INSTRUCTOR', 'ADMIN'), validateRequest, progressController.getCourseProgressAnalytics)

// User progress (for admins)
router.get('/user/:userId', authorize('ADMIN'), validateRequest, progressController.getUserProgress)
router.get('/user/:userId/courses', authorize('ADMIN'), validateRequest, progressController.getUserCourseProgress)

// Progress analytics (for admins)
router.get('/analytics/overview', authorize('ADMIN'), progressController.getProgressOverview)
router.get('/analytics/trends', authorize('ADMIN'), progressController.getProgressTrends)

export default router

import { Router } from 'express'
import { adaptiveController } from '@/controllers/adaptiveController'
import { authMiddleware } from '@/middleware/auth'

const router = Router()

// All routes require authentication
router.use(authMiddleware)

// Adaptive learning routes
router.get('/recommendations', adaptiveController.getRecommendations)
router.get('/learning-path', adaptiveController.getLearningPath)
router.post('/learning-path/generate', adaptiveController.generateLearningPath)
router.put('/learning-path', adaptiveController.updateLearningPath)

// Learning analytics for adaptive system
router.get('/learning-patterns', adaptiveController.getLearningPatterns)
router.get('/strengths-weaknesses', adaptiveController.getStrengthsWeaknesses)
router.get('/difficulty-adjustment', adaptiveController.getDifficultyAdjustment)

// Adaptive content delivery
router.get('/next-content', adaptiveController.getNextContent)
router.post('/content-feedback', adaptiveController.submitContentFeedback)

export default router

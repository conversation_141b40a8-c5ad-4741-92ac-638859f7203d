import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export const analyticsController = {
  // Student analytics dashboard
  async getMyDashboard(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const [userStats, recentProgress, upcomingDeadlines, achievements] = await Promise.all([
        prisma.userStats.findUnique({
          where: { userId },
        }),
        this.getRecentProgress(userId),
        this.getUpcomingDeadlines(userId),
        this.getRecentAchievements(userId),
      ])

      const learningStreak = await this.calculateLearningStreak(userId)
      const weeklyGoal = await this.getWeeklyGoal(userId)
      const performanceTrend = await this.getPerformanceTrend(userId)

      res.json({
        success: true,
        data: {
          stats: userStats || {
            coursesEnrolled: 0,
            coursesCompleted: 0,
            totalLearningTime: 0,
            streakDays: 0,
            averageScore: 0,
            totalPoints: 0,
            level: 1,
          },
          learningStreak,
          weeklyGoal,
          recentProgress,
          upcomingDeadlines,
          achievements,
          performanceTrend,
        },
      })
    } catch (error) {
      logger.error('Get my dashboard error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Student learning time analytics
  async getMyLearningTime(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id
      const { period = '30' } = req.query
      const days = parseInt(period as string)

      const learningTimeData = await this.getLearningTimeData(userId, days)
      const timeByCategory = await this.getTimeByCategory(userId, days)
      const dailyAverage = await this.getDailyAverage(userId, days)

      res.json({
        success: true,
        data: {
          period: `${days} days`,
          dailyData: learningTimeData,
          categoryBreakdown: timeByCategory,
          summary: {
            totalTime: learningTimeData.reduce((sum, day) => sum + day.timeSpent, 0),
            dailyAverage,
            activeDays: learningTimeData.filter(day => day.timeSpent > 0).length,
          },
        },
      })
    } catch (error) {
      logger.error('Get my learning time error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Student performance analytics
  async getMyPerformance(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user!.id

      const [quizPerformance, courseProgress, skillAssessment] = await Promise.all([
        this.getQuizPerformance(userId),
        this.getCourseProgressAnalytics(userId),
        this.getSkillAssessment(userId),
      ])

      const improvementAreas = await this.getImprovementAreas(userId)
      const strengths = await this.getStrengths(userId)

      res.json({
        success: true,
        data: {
          quizPerformance,
          courseProgress,
          skillAssessment,
          improvementAreas,
          strengths,
        },
      })
    } catch (error) {
      logger.error('Get my performance error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Instructor dashboard
  async getInstructorDashboard(req: AuthenticatedRequest, res: Response) {
    try {
      const instructorId = req.user!.id

      const [courseStats, studentStats, recentActivity, revenue] = await Promise.all([
        this.getInstructorCourseStats(instructorId),
        this.getInstructorStudentStats(instructorId),
        this.getInstructorRecentActivity(instructorId),
        this.getInstructorRevenue(instructorId),
      ])

      res.json({
        success: true,
        data: {
          courseStats,
          studentStats,
          recentActivity,
          revenue,
        },
      })
    } catch (error) {
      logger.error('Get instructor dashboard error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Instructor course analytics
  async getInstructorCourseAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const instructorId = req.user!.id

      const courses = await prisma.course.findMany({
        where: { instructorId },
        include: {
          _count: {
            select: {
              enrollments: true,
              lessons: true,
              reviews: true,
            },
          },
          reviews: {
            select: { rating: true },
          },
          enrollments: {
            select: {
              enrolledAt: true,
              status: true,
              progress: true,
            },
          },
        },
      })

      const courseAnalytics = courses.map(course => {
        const averageRating = course.reviews.length > 0
          ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
          : 0

        const averageProgress = course.enrollments.length > 0
          ? course.enrollments.reduce((sum, enrollment) => sum + enrollment.progress, 0) / course.enrollments.length
          : 0

        const completionRate = course.enrollments.length > 0
          ? (course.enrollments.filter(e => e.status === 'COMPLETED').length / course.enrollments.length) * 100
          : 0

        return {
          id: course.id,
          title: course.title,
          category: course.category,
          enrollmentCount: course._count.enrollments,
          lessonCount: course._count.lessons,
          reviewCount: course._count.reviews,
          averageRating: Math.round(averageRating * 10) / 10,
          averageProgress: Math.round(averageProgress),
          completionRate: Math.round(completionRate),
          revenue: course.enrollments.length * course.price,
        }
      })

      res.json({
        success: true,
        data: courseAnalytics,
      })
    } catch (error) {
      logger.error('Get instructor course analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Instructor student analytics
  async getInstructorStudentAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const instructorId = req.user!.id

      const enrollments = await prisma.enrollment.findMany({
        where: {
          course: {
            instructorId,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          course: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      })

      const studentAnalytics = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId: enrollment.userId,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const totalLessons = await prisma.lesson.count({
            where: {
              courseId: enrollment.courseId,
              isPublished: true,
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            student: enrollment.user,
            course: enrollment.course,
            enrollment: {
              enrolledAt: enrollment.enrolledAt,
              status: enrollment.status,
              progress: enrollment.progress,
            },
            detailed: {
              completedLessons,
              totalLessons,
              timeSpent: totalTimeSpent,
              lastActivity: progress.length > 0
                ? Math.max(...progress.map(p => p.lastAccessed.getTime()))
                : enrollment.enrolledAt.getTime(),
            },
          }
        })
      )

      res.json({
        success: true,
        data: studentAnalytics,
      })
    } catch (error) {
      logger.error('Get instructor student analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Admin dashboard
  async getAdminDashboard(req: AuthenticatedRequest, res: Response) {
    try {
      const [platformStats, userGrowth, courseStats, revenueStats] = await Promise.all([
        this.getPlatformStats(),
        this.getUserGrowthStats(),
        this.getAdminCourseStats(),
        this.getRevenueStats(),
      ])

      const engagementMetrics = await this.getEngagementMetrics()
      const topCourses = await this.getTopCourses()
      const topInstructors = await this.getTopInstructors()

      res.json({
        success: true,
        data: {
          platformStats,
          userGrowth,
          courseStats,
          revenueStats,
          engagementMetrics,
          topCourses,
          topInstructors,
        },
      })
    } catch (error) {
      logger.error('Get admin dashboard error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Admin user analytics
  async getUserAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const [userStats, registrationTrends, activityMetrics, retentionStats] = await Promise.all([
        this.getDetailedUserStats(),
        this.getRegistrationTrends(),
        this.getUserActivityMetrics(),
        this.getRetentionStats(),
      ])

      res.json({
        success: true,
        data: {
          userStats,
          registrationTrends,
          activityMetrics,
          retentionStats,
        },
      })
    } catch (error) {
      logger.error('Get user analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Admin course analytics
  async getCourseAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const [courseStats, popularCategories, completionRates, performanceMetrics] = await Promise.all([
        this.getDetailedCourseStats(),
        this.getPopularCategories(),
        this.getCourseCompletionRates(),
        this.getCoursePerformanceMetrics(),
      ])

      res.json({
        success: true,
        data: {
          courseStats,
          popularCategories,
          completionRates,
          performanceMetrics,
        },
      })
    } catch (error) {
      logger.error('Get course analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Admin revenue analytics
  async getRevenueAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const [revenueOverview, monthlyRevenue, revenueByCategory, topEarningCourses] = await Promise.all([
        this.getRevenueOverview(),
        this.getMonthlyRevenue(),
        this.getRevenueByCategory(),
        this.getTopEarningCourses(),
      ])

      res.json({
        success: true,
        data: {
          overview: revenueOverview,
          monthlyTrends: monthlyRevenue,
          categoryBreakdown: revenueByCategory,
          topCourses: topEarningCourses,
        },
      })
    } catch (error) {
      logger.error('Get revenue analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Admin engagement analytics
  async getEngagementAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const [dailyActiveUsers, sessionMetrics, contentEngagement, learningPatterns] = await Promise.all([
        this.getDailyActiveUsers(),
        this.getSessionMetrics(),
        this.getContentEngagement(),
        this.getLearningPatterns(),
      ])

      res.json({
        success: true,
        data: {
          dailyActiveUsers,
          sessionMetrics,
          contentEngagement,
          learningPatterns,
        },
      })
    } catch (error) {
      logger.error('Get engagement analytics error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Helper methods (simplified implementations)
  async getRecentProgress(userId: string) {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    return await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: sevenDaysAgo },
      },
      include: {
        lesson: {
          include: {
            course: {
              select: { title: true },
            },
          },
        },
      },
      orderBy: { lastAccessed: 'desc' },
      take: 5,
    })
  },

  async getUpcomingDeadlines(userId: string) {
    // Placeholder - would implement assignment deadlines
    return []
  },

  async getRecentAchievements(userId: string) {
    return await prisma.userBadge.findMany({
      where: { userId },
      include: { badge: true },
      orderBy: { earnedAt: 'desc' },
      take: 3,
    })
  },

  async calculateLearningStreak(userId: string) {
    // Simplified streak calculation
    const progress = await prisma.progress.findMany({
      where: { userId },
      select: { lastAccessed: true },
      orderBy: { lastAccessed: 'desc' },
    })

    const dates = progress.map(p => p.lastAccessed.toDateString())
    const uniqueDates = [...new Set(dates)]

    let streak = 0
    const today = new Date().toDateString()
    
    if (uniqueDates.includes(today)) {
      streak = 1
      // Calculate consecutive days
      for (let i = 1; i < uniqueDates.length; i++) {
        const currentDate = new Date(uniqueDates[i])
        const previousDate = new Date(uniqueDates[i - 1])
        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
        
        if (dayDiff === 1) {
          streak++
        } else {
          break
        }
      }
    }

    return { current: streak, longest: streak }
  },

  async getWeeklyGoal(userId: string) {
    // Placeholder - would implement user-defined weekly goals
    return {
      target: 300, // minutes
      current: 180,
      percentage: 60,
    }
  },

  async getPerformanceTrend(userId: string) {
    const quizAttempts = await prisma.quizAttempt.findMany({
      where: { userId },
      select: { score: true, completedAt: true },
      orderBy: { completedAt: 'asc' },
      take: 10,
    })

    return quizAttempts.map(attempt => ({
      date: attempt.completedAt,
      score: attempt.score,
    }))
  },

  async getLearningTimeData(userId: string, days: number) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const progress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: startDate },
      },
      select: {
        timeSpent: true,
        lastAccessed: true,
      },
    })

    // Group by day
    const dailyData = {}
    progress.forEach(p => {
      const day = p.lastAccessed.toDateString()
      dailyData[day] = (dailyData[day] || 0) + p.timeSpent
    })

    return Object.entries(dailyData).map(([date, timeSpent]) => ({
      date,
      timeSpent,
    }))
  },

  async getTimeByCategory(userId: string, days: number) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const progress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: startDate },
      },
      include: {
        lesson: {
          include: {
            course: {
              select: { category: true },
            },
          },
        },
      },
    })

    const categoryTime = {}
    progress.forEach(p => {
      const category = p.lesson.course.category
      categoryTime[category] = (categoryTime[category] || 0) + p.timeSpent
    })

    return Object.entries(categoryTime).map(([category, time]) => ({
      category,
      time,
    }))
  },

  async getDailyAverage(userId: string, days: number) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const totalTime = await prisma.progress.aggregate({
      where: {
        userId,
        lastAccessed: { gte: startDate },
      },
      _sum: { timeSpent: true },
    })

    return Math.round((totalTime._sum.timeSpent || 0) / days)
  },

  async getQuizPerformance(userId: string) {
    const attempts = await prisma.quizAttempt.findMany({
      where: { userId },
      include: {
        quiz: {
          include: {
            course: {
              select: { category: true },
            },
          },
        },
      },
    })

    const averageScore = attempts.length > 0
      ? attempts.reduce((sum, attempt) => sum + attempt.score, 0) / attempts.length
      : 0

    return {
      totalAttempts: attempts.length,
      averageScore: Math.round(averageScore),
      recentTrend: attempts.slice(-5).map(a => a.score),
    }
  },

  async getCourseProgressAnalytics(userId: string) {
    const enrollments = await prisma.enrollment.findMany({
      where: { userId },
      include: {
        course: {
          select: {
            title: true,
            category: true,
          },
        },
      },
    })

    return enrollments.map(enrollment => ({
      courseTitle: enrollment.course.title,
      category: enrollment.course.category,
      progress: enrollment.progress,
      status: enrollment.status,
      enrolledAt: enrollment.enrolledAt,
    }))
  },

  async getSkillAssessment(userId: string) {
    // Placeholder - would implement skill assessment based on performance
    return {
      programming: 75,
      mathematics: 60,
      design: 80,
      communication: 70,
    }
  },

  async getImprovementAreas(userId: string) {
    // Placeholder - would analyze weak performance areas
    return [
      { area: 'Advanced JavaScript', score: 45, priority: 'high' },
      { area: 'Database Design', score: 55, priority: 'medium' },
    ]
  },

  async getStrengths(userId: string) {
    // Placeholder - would analyze strong performance areas
    return [
      { area: 'HTML/CSS', score: 90, confidence: 'high' },
      { area: 'React Basics', score: 85, confidence: 'high' },
    ]
  },

  // Additional helper methods would be implemented here...
  // For brevity, I'm providing simplified implementations

  async getInstructorCourseStats(instructorId: string) {
    const courses = await prisma.course.findMany({
      where: { instructorId },
      include: {
        _count: {
          select: { enrollments: true },
        },
      },
    })

    return {
      totalCourses: courses.length,
      publishedCourses: courses.filter(c => c.isPublished).length,
      totalEnrollments: courses.reduce((sum, c) => sum + c._count.enrollments, 0),
    }
  },

  async getInstructorStudentStats(instructorId: string) {
    const enrollments = await prisma.enrollment.findMany({
      where: {
        course: { instructorId },
      },
    })

    return {
      totalStudents: enrollments.length,
      activeStudents: enrollments.filter(e => e.status === 'ACTIVE').length,
      completedStudents: enrollments.filter(e => e.status === 'COMPLETED').length,
    }
  },

  async getInstructorRecentActivity(instructorId: string) {
    // Placeholder - would implement recent activity tracking
    return []
  },

  async getInstructorRevenue(instructorId: string) {
    const courses = await prisma.course.findMany({
      where: { instructorId },
      include: {
        enrollments: true,
      },
    })

    const totalRevenue = courses.reduce((sum, course) => {
      return sum + (course.enrollments.length * course.price)
    }, 0)

    return {
      total: totalRevenue,
      thisMonth: totalRevenue * 0.1, // Placeholder
      growth: 15, // Placeholder percentage
    }
  },

  async getPlatformStats() {
    const [totalUsers, totalCourses, totalEnrollments] = await Promise.all([
      prisma.user.count(),
      prisma.course.count(),
      prisma.enrollment.count(),
    ])

    return {
      totalUsers,
      totalCourses,
      totalEnrollments,
      activeUsers: Math.round(totalUsers * 0.7), // Placeholder
    }
  },

  async getUserGrowthStats() {
    // Placeholder - would implement actual growth calculation
    return {
      thisMonth: 150,
      lastMonth: 120,
      growth: 25,
    }
  },

  async getAdminCourseStats() {
    const courses = await prisma.course.findMany({
      include: {
        _count: {
          select: { enrollments: true },
        },
      },
    })

    return {
      total: courses.length,
      published: courses.filter(c => c.isPublished).length,
      averageEnrollments: courses.length > 0
        ? courses.reduce((sum, c) => sum + c._count.enrollments, 0) / courses.length
        : 0,
    }
  },

  async getRevenueStats() {
    // Placeholder - would implement actual revenue calculation
    return {
      total: 50000,
      thisMonth: 8500,
      growth: 12,
    }
  },

  async getEngagementMetrics() {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const activeUsers = await prisma.progress.groupBy({
      by: ['userId'],
      where: {
        lastAccessed: { gte: sevenDaysAgo },
      },
    })

    return {
      dailyActiveUsers: activeUsers.length,
      averageSessionTime: 45, // Placeholder
      engagementRate: 68, // Placeholder
    }
  },

  async getTopCourses() {
    return await prisma.course.findMany({
      include: {
        _count: {
          select: { enrollments: true },
        },
        instructor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        enrollments: {
          _count: 'desc',
        },
      },
      take: 5,
    })
  },

  async getTopInstructors() {
    // Placeholder - would implement instructor ranking
    return []
  },

  async getDetailedUserStats() {
    const [students, instructors, admins] = await Promise.all([
      prisma.user.count({ where: { role: 'STUDENT' } }),
      prisma.user.count({ where: { role: 'INSTRUCTOR' } }),
      prisma.user.count({ where: { role: 'ADMIN' } }),
    ])

    return { students, instructors, admins }
  },

  async getRegistrationTrends() {
    // Placeholder - would implement registration trend analysis
    return []
  },

  async getUserActivityMetrics() {
    // Placeholder - would implement user activity analysis
    return {}
  },

  async getRetentionStats() {
    // Placeholder - would implement retention analysis
    return {}
  },

  async getDetailedCourseStats() {
    // Placeholder - would implement detailed course statistics
    return {}
  },

  async getPopularCategories() {
    const courses = await prisma.course.groupBy({
      by: ['category'],
      _count: { category: true },
      orderBy: {
        _count: {
          category: 'desc',
        },
      },
    })

    return courses.map(c => ({
      category: c.category,
      count: c._count.category,
    }))
  },

  async getCourseCompletionRates() {
    // Placeholder - would implement completion rate analysis
    return {}
  },

  async getCoursePerformanceMetrics() {
    // Placeholder - would implement course performance analysis
    return {}
  },

  async getRevenueOverview() {
    // Placeholder - would implement revenue overview
    return {}
  },

  async getMonthlyRevenue() {
    // Placeholder - would implement monthly revenue trends
    return []
  },

  async getRevenueByCategory() {
    // Placeholder - would implement revenue by category
    return {}
  },

  async getTopEarningCourses() {
    // Placeholder - would implement top earning courses
    return []
  },

  async getDailyActiveUsers() {
    // Placeholder - would implement DAU calculation
    return []
  },

  async getSessionMetrics() {
    // Placeholder - would implement session metrics
    return {}
  },

  async getContentEngagement() {
    // Placeholder - would implement content engagement analysis
    return {}
  },

  async getLearningPatterns() {
    // Placeholder - would implement learning pattern analysis
    return {}
  },
}

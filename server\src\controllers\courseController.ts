import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError, NotFoundError, ValidationError } from '@/utils/AppError'
import { logger } from '@/utils/logger'
import { uploadToCloudinary } from '@/utils/cloudinary'
import { sendEmail } from '@/utils/email'

const prisma = new PrismaClient()

export const courseController = {
  // Get all courses with filtering and pagination
  async getCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const {
        page = 1,
        limit = 12,
        category,
        level,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        priceMin,
        priceMax,
      } = req.query

      const skip = (Number(page) - 1) * Number(limit)
      const take = Number(limit)

      // Build where clause
      const where: any = {
        isPublished: true,
      }

      if (category) {
        where.category = category
      }

      if (level) {
        where.level = level
      }

      if (search) {
        where.OR = [
          { title: { contains: search as string, mode: 'insensitive' } },
          { description: { contains: search as string, mode: 'insensitive' } },
        ]
      }

      if (priceMin || priceMax) {
        where.price = {}
        if (priceMin) where.price.gte = Number(priceMin)
        if (priceMax) where.price.lte = Number(priceMax)
      }

      // Build orderBy clause
      const orderBy: any = {}
      orderBy[sortBy as string] = sortOrder

      const [courses, total] = await Promise.all([
        prisma.course.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            instructor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
            _count: {
              select: {
                enrollments: true,
                reviews: true,
              },
            },
            reviews: {
              select: {
                rating: true,
              },
            },
          },
        }),
        prisma.course.count({ where }),
      ])

      // Calculate average ratings
      const coursesWithRatings = courses.map(course => {
        const avgRating = course.reviews.length > 0
          ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
          : 0

        return {
          ...course,
          averageRating: Math.round(avgRating * 10) / 10,
          enrollmentCount: course._count.enrollments,
          reviewCount: course._count.reviews,
          reviews: undefined,
          _count: undefined,
        }
      })

      res.json({
        success: true,
        data: {
          courses: coursesWithRatings,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      })
    } catch (error) {
      logger.error('Get courses error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course by ID
  async getCourseById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const course = await prisma.course.findUnique({
        where: { id },
        include: {
          instructor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
              bio: true,
            },
          },
          lessons: {
            where: { isPublished: true },
            orderBy: { order: 'asc' },
            select: {
              id: true,
              title: true,
              description: true,
              duration: true,
              order: true,
            },
          },
          tags: {
            select: {
              tag: true,
            },
          },
          reviews: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  avatar: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          _count: {
            select: {
              enrollments: true,
              lessons: true,
            },
          },
        },
      })

      if (!course) {
        throw new NotFoundError('Course not found')
      }

      // Check if user is enrolled (if authenticated)
      let isEnrolled = false
      let userProgress = null

      if (req.user) {
        const enrollment = await prisma.enrollment.findUnique({
          where: {
            userId_courseId: {
              userId: req.user.id,
              courseId: id,
            },
          },
        })

        isEnrolled = !!enrollment
        
        if (isEnrolled) {
          // Get user progress
          const progress = await prisma.progress.findMany({
            where: {
              userId: req.user.id,
              lesson: {
                courseId: id,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = course._count.lessons
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

          userProgress = {
            completedLessons,
            totalLessons,
            progressPercentage: Math.round(progressPercentage),
          }
        }
      }

      // Calculate average rating
      const avgRating = course.reviews.length > 0
        ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
        : 0

      const courseData = {
        ...course,
        averageRating: Math.round(avgRating * 10) / 10,
        enrollmentCount: course._count.enrollments,
        lessonCount: course._count.lessons,
        tags: course.tags.map(t => t.tag),
        isEnrolled,
        userProgress,
        _count: undefined,
      }

      res.json({
        success: true,
        data: courseData,
      })
    } catch (error) {
      logger.error('Get course by ID error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Create new course
  async createCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { title, description, category, level, duration, price, tags } = req.body

      const course = await prisma.course.create({
        data: {
          title,
          description,
          category,
          level,
          duration,
          price,
          instructorId: req.user!.id,
          tags: tags ? {
            create: tags.map((tag: string) => ({ tag })),
          } : undefined,
        },
        include: {
          instructor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          tags: {
            select: {
              tag: true,
            },
          },
        },
      })

      res.status(201).json({
        success: true,
        message: 'Course created successfully',
        data: {
          ...course,
          tags: course.tags.map(t => t.tag),
        },
      })
    } catch (error) {
      logger.error('Create course error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Update course
  async updateCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { title, description, category, level, duration, price, tags } = req.body

      const updateData: any = {}
      if (title) updateData.title = title
      if (description) updateData.description = description
      if (category) updateData.category = category
      if (level) updateData.level = level
      if (duration) updateData.duration = duration
      if (price !== undefined) updateData.price = price

      const course = await prisma.course.update({
        where: { id },
        data: updateData,
        include: {
          instructor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          tags: {
            select: {
              tag: true,
            },
          },
        },
      })

      // Update tags if provided
      if (tags) {
        await prisma.courseTag.deleteMany({
          where: { courseId: id },
        })

        await prisma.courseTag.createMany({
          data: tags.map((tag: string) => ({
            courseId: id,
            tag,
          })),
        })
      }

      res.json({
        success: true,
        message: 'Course updated successfully',
        data: {
          ...course,
          tags: course.tags.map(t => t.tag),
        },
      })
    } catch (error) {
      logger.error('Update course error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Delete course
  async deleteCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      await prisma.course.delete({
        where: { id },
      })

      res.json({
        success: true,
        message: 'Course deleted successfully',
      })
    } catch (error) {
      logger.error('Delete course error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Publish course
  async publishCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      // Check if course has at least one lesson
      const lessonCount = await prisma.lesson.count({
        where: { courseId: id },
      })

      if (lessonCount === 0) {
        throw new ValidationError('Course must have at least one lesson before publishing')
      }

      await prisma.course.update({
        where: { id },
        data: { isPublished: true },
      })

      res.json({
        success: true,
        message: 'Course published successfully',
      })
    } catch (error) {
      logger.error('Publish course error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Unpublish course
  async unpublishCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      await prisma.course.update({
        where: { id },
        data: { isPublished: false },
      })

      res.json({
        success: true,
        message: 'Course unpublished successfully',
      })
    } catch (error) {
      logger.error('Unpublish course error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Enroll in course
  async enrollInCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      // Check if course exists and is published
      const course = await prisma.course.findUnique({
        where: { id },
        select: {
          id: true,
          title: true,
          isPublished: true,
          instructor: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      })

      if (!course) {
        throw new NotFoundError('Course not found')
      }

      if (!course.isPublished) {
        throw new ValidationError('Course is not published')
      }

      // Check if already enrolled
      const existingEnrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      if (existingEnrollment) {
        throw new ValidationError('Already enrolled in this course')
      }

      // Create enrollment
      await prisma.enrollment.create({
        data: {
          userId: req.user!.id,
          courseId: id,
        },
      })

      // Update user stats
      await prisma.userStats.update({
        where: { userId: req.user!.id },
        data: {
          coursesEnrolled: {
            increment: 1,
          },
        },
      })

      // Send enrollment confirmation email
      try {
        await sendEmail({
          to: req.user!.email,
          subject: `Enrolled in ${course.title}`,
          template: 'course-enrollment',
          data: {
            firstName: req.user!.firstName,
            courseName: course.title,
            instructorName: `${course.instructor.firstName} ${course.instructor.lastName}`,
            courseUrl: `${process.env.CLIENT_URL}/courses/${id}`,
          },
        })
      } catch (emailError) {
        logger.error('Failed to send enrollment email:', emailError)
      }

      res.status(201).json({
        success: true,
        message: 'Successfully enrolled in course',
      })
    } catch (error) {
      logger.error('Enroll in course error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Unenroll from course
  async unenrollFromCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      if (!enrollment) {
        throw new NotFoundError('Enrollment not found')
      }

      await prisma.enrollment.delete({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      // Update user stats
      await prisma.userStats.update({
        where: { userId: req.user!.id },
        data: {
          coursesEnrolled: {
            decrement: 1,
          },
        },
      })

      res.json({
        success: true,
        message: 'Successfully unenrolled from course',
      })
    } catch (error) {
      logger.error('Unenroll from course error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get enrollment status
  async getEnrollmentStatus(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      res.json({
        success: true,
        data: {
          isEnrolled: !!enrollment,
          enrollment: enrollment || null,
        },
      })
    } catch (error) {
      logger.error('Get enrollment status error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course categories
  async getCategories(req: AuthenticatedRequest, res: Response) {
    try {
      const categories = await prisma.course.findMany({
        where: { isPublished: true },
        select: { category: true },
        distinct: ['category'],
      })

      const categoryList = categories.map(c => c.category).sort()

      res.json({
        success: true,
        data: categoryList,
      })
    } catch (error) {
      logger.error('Get categories error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get featured courses
  async getFeaturedCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const courses = await prisma.course.findMany({
        where: { isPublished: true },
        take: 6,
        orderBy: [
          { enrollments: { _count: 'desc' } },
          { createdAt: 'desc' },
        ],
        include: {
          instructor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          _count: {
            select: {
              enrollments: true,
              reviews: true,
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
      })

      const coursesWithRatings = courses.map(course => {
        const avgRating = course.reviews.length > 0
          ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
          : 0

        return {
          ...course,
          averageRating: Math.round(avgRating * 10) / 10,
          enrollmentCount: course._count.enrollments,
          reviewCount: course._count.reviews,
          reviews: undefined,
          _count: undefined,
        }
      })

      res.json({
        success: true,
        data: coursesWithRatings,
      })
    } catch (error) {
      logger.error('Get featured courses error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course preview (limited info for non-enrolled users)
  async getCoursePreview(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const course = await prisma.course.findUnique({
        where: { id },
        select: {
          id: true,
          title: true,
          description: true,
          thumbnail: true,
          category: true,
          level: true,
          duration: true,
          price: true,
          instructor: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true,
              bio: true,
            },
          },
          lessons: {
            where: { isPublished: true },
            select: {
              title: true,
              description: true,
              duration: true,
              order: true,
            },
            orderBy: { order: 'asc' },
            take: 3, // Only show first 3 lessons in preview
          },
          _count: {
            select: {
              enrollments: true,
              lessons: true,
            },
          },
        },
      })

      if (!course) {
        throw new NotFoundError('Course not found')
      }

      res.json({
        success: true,
        data: {
          ...course,
          enrollmentCount: course._count.enrollments,
          lessonCount: course._count.lessons,
          _count: undefined,
        },
      })
    } catch (error) {
      logger.error('Get course preview error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Upload course thumbnail
  async uploadCourseThumbnail(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      if (!req.file) {
        throw new ValidationError('No file uploaded')
      }

      const thumbnailUrl = await uploadToCloudinary(req.file.buffer, 'course-thumbnails')

      await prisma.course.update({
        where: { id },
        data: { thumbnail: thumbnailUrl },
      })

      res.json({
        success: true,
        message: 'Thumbnail uploaded successfully',
        data: { thumbnailUrl },
      })
    } catch (error) {
      logger.error('Upload course thumbnail error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Add course review
  async addCourseReview(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { rating, comment } = req.body

      // Check if user is enrolled
      const enrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      if (!enrollment) {
        throw new ValidationError('You must be enrolled in the course to leave a review')
      }

      // Check if user already reviewed
      const existingReview = await prisma.courseReview.findUnique({
        where: {
          userId_courseId: {
            userId: req.user!.id,
            courseId: id,
          },
        },
      })

      if (existingReview) {
        // Update existing review
        const review = await prisma.courseReview.update({
          where: {
            userId_courseId: {
              userId: req.user!.id,
              courseId: id,
            },
          },
          data: { rating, comment },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        })

        res.json({
          success: true,
          message: 'Review updated successfully',
          data: review,
        })
      } else {
        // Create new review
        const review = await prisma.courseReview.create({
          data: {
            userId: req.user!.id,
            courseId: id,
            rating,
            comment,
          },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        })

        res.status(201).json({
          success: true,
          message: 'Review added successfully',
          data: review,
        })
      }
    } catch (error) {
      logger.error('Add course review error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get my enrolled courses
  async getMyEnrolledCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const enrollments = await prisma.enrollment.findMany({
        where: { userId: req.user!.id },
        include: {
          course: {
            include: {
              instructor: {
                select: {
                  firstName: true,
                  lastName: true,
                  avatar: true,
                },
              },
              _count: {
                select: {
                  lessons: true,
                },
              },
            },
          },
        },
        orderBy: { enrolledAt: 'desc' },
      })

      // Get progress for each course
      const coursesWithProgress = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId: req.user!.id,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = enrollment.course._count.lessons
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

          return {
            ...enrollment,
            progress: {
              completedLessons,
              totalLessons,
              progressPercentage: Math.round(progressPercentage),
            },
          }
        })
      )

      res.json({
        success: true,
        data: coursesWithProgress,
      })
    } catch (error) {
      logger.error('Get my enrolled courses error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get my created courses (for instructors)
  async getMyCreatedCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const courses = await prisma.course.findMany({
        where: { instructorId: req.user!.id },
        include: {
          _count: {
            select: {
              enrollments: true,
              lessons: true,
              reviews: true,
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })

      const coursesWithStats = courses.map(course => {
        const avgRating = course.reviews.length > 0
          ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
          : 0

        return {
          ...course,
          averageRating: Math.round(avgRating * 10) / 10,
          enrollmentCount: course._count.enrollments,
          lessonCount: course._count.lessons,
          reviewCount: course._count.reviews,
          reviews: undefined,
          _count: undefined,
        }
      })

      res.json({
        success: true,
        data: coursesWithStats,
      })
    } catch (error) {
      logger.error('Get my created courses error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get my overall progress
  async getMyProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const enrollments = await prisma.enrollment.findMany({
        where: { userId: req.user!.id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              _count: {
                select: {
                  lessons: true,
                },
              },
            },
          },
        },
      })

      const progressData = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId: req.user!.id,
              lesson: {
                courseId: enrollment.courseId,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalLessons = enrollment.course._count.lessons
          const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            courseId: enrollment.courseId,
            courseTitle: enrollment.course.title,
            completedLessons,
            totalLessons,
            progressPercentage: Math.round(progressPercentage),
            timeSpent: totalTimeSpent,
            enrolledAt: enrollment.enrolledAt,
            status: enrollment.status,
          }
        })
      )

      res.json({
        success: true,
        data: progressData,
      })
    } catch (error) {
      logger.error('Get my progress error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course analytics (for instructors)
  async getCourseAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const [course, enrollments, reviews, progress] = await Promise.all([
        prisma.course.findUnique({
          where: { id },
          select: {
            title: true,
            createdAt: true,
            _count: {
              select: {
                enrollments: true,
                lessons: true,
              },
            },
          },
        }),
        prisma.enrollment.findMany({
          where: { courseId: id },
          select: {
            enrolledAt: true,
            status: true,
            progress: true,
          },
        }),
        prisma.courseReview.findMany({
          where: { courseId: id },
          select: {
            rating: true,
            createdAt: true,
          },
        }),
        prisma.progress.findMany({
          where: {
            lesson: {
              courseId: id,
            },
          },
          select: {
            isCompleted: true,
            timeSpent: true,
            userId: true,
          },
        }),
      ])

      if (!course) {
        throw new NotFoundError('Course not found')
      }

      // Calculate analytics
      const totalEnrollments = enrollments.length
      const activeEnrollments = enrollments.filter(e => e.status === 'ACTIVE').length
      const completedEnrollments = enrollments.filter(e => e.status === 'COMPLETED').length
      const averageProgress = enrollments.reduce((sum, e) => sum + e.progress, 0) / totalEnrollments || 0

      const averageRating = reviews.length > 0
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
        : 0

      const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)
      const completionRate = progress.length > 0
        ? (progress.filter(p => p.isCompleted).length / progress.length) * 100
        : 0

      // Enrollment trends (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentEnrollments = enrollments.filter(e => e.enrolledAt >= thirtyDaysAgo)

      res.json({
        success: true,
        data: {
          course: {
            title: course.title,
            createdAt: course.createdAt,
            lessonCount: course._count.lessons,
          },
          enrollments: {
            total: totalEnrollments,
            active: activeEnrollments,
            completed: completedEnrollments,
            recent: recentEnrollments.length,
          },
          progress: {
            average: Math.round(averageProgress),
            completionRate: Math.round(completionRate),
            totalTimeSpent,
          },
          reviews: {
            count: reviews.length,
            averageRating: Math.round(averageRating * 10) / 10,
          },
        },
      })
    } catch (error) {
      logger.error('Get course analytics error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get course students (for instructors)
  async getCourseStudents(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const enrollments = await prisma.enrollment.findMany({
        where: { courseId: id },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: { enrolledAt: 'desc' },
      })

      // Get progress for each student
      const studentsWithProgress = await Promise.all(
        enrollments.map(async (enrollment) => {
          const progress = await prisma.progress.findMany({
            where: {
              userId: enrollment.userId,
              lesson: {
                courseId: id,
              },
            },
          })

          const completedLessons = progress.filter(p => p.isCompleted).length
          const totalTimeSpent = progress.reduce((sum, p) => sum + p.timeSpent, 0)

          return {
            ...enrollment,
            progress: {
              completedLessons,
              totalTimeSpent,
              lastAccessed: progress.length > 0
                ? Math.max(...progress.map(p => p.lastAccessed.getTime()))
                : null,
            },
          }
        })
      )

      res.json({
        success: true,
        data: studentsWithProgress,
      })
    } catch (error) {
      logger.error('Get course students error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get course progress overview (for instructors)
  async getCourseProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const lessons = await prisma.lesson.findMany({
        where: { courseId: id },
        select: {
          id: true,
          title: true,
          order: true,
          progress: {
            select: {
              userId: true,
              isCompleted: true,
              timeSpent: true,
            },
          },
        },
        orderBy: { order: 'asc' },
      })

      const totalStudents = await prisma.enrollment.count({
        where: { courseId: id },
      })

      const lessonProgress = lessons.map(lesson => {
        const completedCount = lesson.progress.filter(p => p.isCompleted).length
        const totalTimeSpent = lesson.progress.reduce((sum, p) => sum + p.timeSpent, 0)
        const completionRate = totalStudents > 0 ? (completedCount / totalStudents) * 100 : 0

        return {
          lessonId: lesson.id,
          title: lesson.title,
          order: lesson.order,
          completedCount,
          completionRate: Math.round(completionRate),
          totalTimeSpent,
          averageTimeSpent: lesson.progress.length > 0
            ? Math.round(totalTimeSpent / lesson.progress.length)
            : 0,
        }
      })

      res.json({
        success: true,
        data: {
          totalStudents,
          lessons: lessonProgress,
        },
      })
    } catch (error) {
      logger.error('Get course progress error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },
}

import { Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuthenticatedRequest } from '@/types/auth'
import { AppError } from '@/utils/AppError'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

export const userController = {
  // Get all users (admin only)
  async getUsers(req: AuthenticatedRequest, res: Response) {
    try {
      const {
        page = 1,
        limit = 20,
        role,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        isActive,
      } = req.query

      const skip = (Number(page) - 1) * Number(limit)
      const take = Number(limit)

      // Build where clause
      const where: any = {}

      if (role) {
        where.role = role
      }

      if (search) {
        where.OR = [
          { firstName: { contains: search as string, mode: 'insensitive' } },
          { lastName: { contains: search as string, mode: 'insensitive' } },
          { email: { contains: search as string, mode: 'insensitive' } },
        ]
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true'
      }

      // Build orderBy clause
      const orderBy: any = {}
      orderBy[sortBy as string] = sortOrder

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take,
          orderBy,
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            avatar: true,
            isActive: true,
            isVerified: true,
            lastLoginAt: true,
            createdAt: true,
            stats: {
              select: {
                coursesEnrolled: true,
                coursesCompleted: true,
                totalLearningTime: true,
                averageScore: true,
              },
            },
            _count: {
              select: {
                enrollments: true,
                coursesCreated: true,
              },
            },
          },
        }),
        prisma.user.count({ where }),
      ])

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      })
    } catch (error) {
      logger.error('Get users error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Get user by ID (admin only)
  async getUserById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          preferences: true,
          stats: true,
          enrollments: {
            include: {
              course: {
                select: {
                  id: true,
                  title: true,
                  category: true,
                },
              },
            },
            orderBy: { enrolledAt: 'desc' },
          },
          coursesCreated: {
            select: {
              id: true,
              title: true,
              category: true,
              isPublished: true,
              _count: {
                select: {
                  enrollments: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          },
          badges: {
            include: {
              badge: true,
            },
            orderBy: { earnedAt: 'desc' },
          },
        },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      res.json({
        success: true,
        data: userWithoutPassword,
      })
    } catch (error) {
      logger.error('Get user by ID error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Update user (admin only)
  async updateUser(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params
      const { firstName, lastName, role, isActive, isVerified } = req.body

      const user = await prisma.user.findUnique({
        where: { id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      const updateData: any = {}
      if (firstName) updateData.firstName = firstName
      if (lastName) updateData.lastName = lastName
      if (role) updateData.role = role
      if (isActive !== undefined) updateData.isActive = isActive
      if (isVerified !== undefined) updateData.isVerified = isVerified

      const updatedUser = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          avatar: true,
          isActive: true,
          isVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      res.json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser,
      })
    } catch (error) {
      logger.error('Update user error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Delete user (admin only)
  async deleteUser(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const user = await prisma.user.findUnique({
        where: { id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Prevent deleting admin users
      if (user.role === 'ADMIN') {
        throw new AppError('Cannot delete admin users', 403)
      }

      await prisma.user.delete({
        where: { id },
      })

      res.json({
        success: true,
        message: 'User deleted successfully',
      })
    } catch (error) {
      logger.error('Delete user error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Activate user (admin only)
  async activateUser(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const user = await prisma.user.findUnique({
        where: { id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      await prisma.user.update({
        where: { id },
        data: { isActive: true },
      })

      res.json({
        success: true,
        message: 'User activated successfully',
      })
    } catch (error) {
      logger.error('Activate user error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Deactivate user (admin only)
  async deactivateUser(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const user = await prisma.user.findUnique({
        where: { id },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Prevent deactivating admin users
      if (user.role === 'ADMIN') {
        throw new AppError('Cannot deactivate admin users', 403)
      }

      await prisma.user.update({
        where: { id },
        data: { isActive: false },
      })

      res.json({
        success: true,
        message: 'User deactivated successfully',
      })
    } catch (error) {
      logger.error('Deactivate user error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get user statistics (admin only)
  async getUserStats(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          stats: true,
          enrollments: {
            include: {
              course: {
                select: {
                  category: true,
                },
              },
            },
          },
          progress: {
            select: {
              timeSpent: true,
              isCompleted: true,
              lastAccessed: true,
            },
          },
          quizAttempts: {
            select: {
              score: true,
              completedAt: true,
            },
          },
        },
      })

      if (!user) {
        throw new AppError('User not found', 404)
      }

      // Calculate additional statistics
      const totalTimeSpent = user.progress.reduce((sum, p) => sum + p.timeSpent, 0)
      const completedLessons = user.progress.filter(p => p.isCompleted).length
      const totalLessons = user.progress.length

      const recentActivity = user.progress
        .filter(p => {
          const daysSinceAccess = (Date.now() - p.lastAccessed.getTime()) / (1000 * 60 * 60 * 24)
          return daysSinceAccess <= 30
        })
        .length

      const quizScores = user.quizAttempts
        .filter(a => a.completedAt)
        .map(a => a.score)

      const averageQuizScore = quizScores.length > 0
        ? quizScores.reduce((sum, score) => sum + score, 0) / quizScores.length
        : 0

      // Category breakdown
      const categoryBreakdown = user.enrollments.reduce((acc, enrollment) => {
        const category = enrollment.course.category
        acc[category] = (acc[category] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const statistics = {
        basic: user.stats,
        detailed: {
          totalTimeSpent,
          completedLessons,
          totalLessons,
          completionRate: totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0,
          recentActivity,
          averageQuizScore: Math.round(averageQuizScore),
          totalQuizAttempts: user.quizAttempts.length,
        },
        categoryBreakdown,
        activityTrend: await this.getUserActivityTrend(id),
      }

      res.json({
        success: true,
        data: statistics,
      })
    } catch (error) {
      logger.error('Get user stats error:', error)
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        })
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        })
      }
    }
  },

  // Get users overview (admin only)
  async getUsersOverview(req: AuthenticatedRequest, res: Response) {
    try {
      const [
        totalUsers,
        activeUsers,
        newUsersThisMonth,
        usersByRole,
        usersByStatus,
        topPerformers,
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        this.getNewUsersThisMonth(),
        this.getUsersByRole(),
        this.getUsersByStatus(),
        this.getTopPerformers(),
      ])

      const overview = {
        totals: {
          totalUsers,
          activeUsers,
          inactiveUsers: totalUsers - activeUsers,
          newUsersThisMonth,
        },
        distribution: {
          byRole: usersByRole,
          byStatus: usersByStatus,
        },
        topPerformers,
        trends: await this.getUserTrends(),
      }

      res.json({
        success: true,
        data: overview,
      })
    } catch (error) {
      logger.error('Get users overview error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      })
    }
  },

  // Helper methods
  async getUserActivityTrend(userId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const progress = await prisma.progress.findMany({
      where: {
        userId,
        lastAccessed: { gte: thirtyDaysAgo },
      },
      select: {
        lastAccessed: true,
        timeSpent: true,
      },
    })

    // Group by day
    const dailyActivity = {}
    progress.forEach(p => {
      const day = p.lastAccessed.toDateString()
      if (!dailyActivity[day]) {
        dailyActivity[day] = { sessions: 0, timeSpent: 0 }
      }
      dailyActivity[day].sessions += 1
      dailyActivity[day].timeSpent += p.timeSpent
    })

    return Object.entries(dailyActivity)
      .map(([date, activity]) => ({
        date,
        ...activity,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  },

  async getNewUsersThisMonth() {
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    return await prisma.user.count({
      where: {
        createdAt: { gte: startOfMonth },
      },
    })
  },

  async getUsersByRole() {
    const users = await prisma.user.groupBy({
      by: ['role'],
      _count: { role: true },
    })

    return users.reduce((acc, user) => {
      acc[user.role] = user._count.role
      return acc
    }, {} as Record<string, number>)
  },

  async getUsersByStatus() {
    const [active, inactive, verified, unverified] = await Promise.all([
      prisma.user.count({ where: { isActive: true } }),
      prisma.user.count({ where: { isActive: false } }),
      prisma.user.count({ where: { isVerified: true } }),
      prisma.user.count({ where: { isVerified: false } }),
    ])

    return {
      active,
      inactive,
      verified,
      unverified,
    }
  },

  async getTopPerformers() {
    const topPerformers = await prisma.user.findMany({
      where: {
        role: 'STUDENT',
        isActive: true,
      },
      include: {
        stats: true,
      },
      orderBy: {
        stats: {
          averageScore: 'desc',
        },
      },
      take: 10,
    })

    return topPerformers.map(user => ({
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      avatar: user.avatar,
      stats: user.stats,
    }))
  },

  async getUserTrends() {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const registrations = await prisma.user.findMany({
      where: {
        createdAt: { gte: thirtyDaysAgo },
      },
      select: {
        createdAt: true,
      },
    })

    // Group by day
    const dailyRegistrations = {}
    registrations.forEach(user => {
      const day = user.createdAt.toDateString()
      dailyRegistrations[day] = (dailyRegistrations[day] || 0) + 1
    })

    return Object.entries(dailyRegistrations)
      .map(([date, count]) => ({
        date,
        registrations: count,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  },
}

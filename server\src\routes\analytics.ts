import { Router } from 'express'
import { analyticsController } from '@/controllers/analyticsController'
import { authMiddleware, authorize } from '@/middleware/auth'

const router = Router()

// All routes require authentication
router.use(authMiddleware)

// Student analytics
router.get('/my/dashboard', analyticsController.getMyDashboard)
router.get('/my/learning-time', analyticsController.getMyLearningTime)
router.get('/my/performance', analyticsController.getMyPerformance)

// Instructor analytics
router.get('/instructor/dashboard', authorize('INSTRUCTOR', 'ADMIN'), analyticsController.getInstructorDashboard)
router.get('/instructor/courses', authorize('INSTRUCTOR', 'ADMIN'), analyticsController.getInstructorCourseAnalytics)
router.get('/instructor/students', authorize('INSTRUCTOR', 'ADMIN'), analyticsController.getInstructorStudentAnalytics)

// Admin analytics
router.get('/admin/dashboard', authorize('ADMIN'), analyticsController.getAdminDashboard)
router.get('/admin/users', authorize('ADMIN'), analyticsController.getUserAnalytics)
router.get('/admin/courses', authorize('ADMIN'), analyticsController.getCourseAnalytics)
router.get('/admin/revenue', authorize('ADMIN'), analyticsController.getRevenueAnalytics)
router.get('/admin/engagement', authorize('ADMIN'), analyticsController.getEngagementAnalytics)

export default router
